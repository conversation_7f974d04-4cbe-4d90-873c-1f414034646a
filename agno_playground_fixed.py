#!/usr/bin/env python3
"""
兼容 ModelScope 的 Agno Playground

由于 ModelScope API 不支持 Agno 默认的消息格式，
这个脚本创建了一个兼容的 Web Playground 界面
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import json
from config import Config
from fastapi import FastAPI, Request, Form
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
import uvicorn
from datetime import datetime

class ModelScopeAgent:
    """兼容 ModelScope API 的智能体类"""
    
    def __init__(self, name, description, instructions=None):
        self.name = name
        self.description = description
        self.instructions = instructions or []
        self.conversation_history = []
        
        self.api_url = f"{Config.MODELSCOPE_BASE_URL}/chat/completions"
        self.headers = {
            "Authorization": f"Bearer {Config.MODELSCOPE_API_KEY}",
            "Content-Type": "application/json"
        }
    
    def _build_messages(self, user_input):
        """构建兼容的消息格式"""
        messages = []
        
        # 添加系统指令（如果有的话）
        if self.instructions:
            system_content = f"{self.description}\\n\\n请遵循以下指令:\\n"
            system_content += "\\n".join(f"- {inst}" for inst in self.instructions)
            messages.append({"role": "system", "content": system_content})
        
        # 添加历史对话
        messages.extend(self.conversation_history)
        
        # 添加用户输入
        messages.append({"role": "user", "content": user_input})
        
        return messages
    
    def chat(self, user_input, max_tokens=1000, temperature=0.7):
        """发送聊天请求"""
        
        messages = self._build_messages(user_input)
        
        data = {
            "model": Config.MODELSCOPE_MODEL,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "enable_thinking": False  # ModelScope 必需参数
        }
        
        try:
            response = requests.post(self.api_url, headers=self.headers, json=data)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result['choices'][0]['message']['content']
                
                # 保存对话历史
                self.conversation_history.append({"role": "user", "content": user_input})
                self.conversation_history.append({"role": "assistant", "content": ai_response})
                
                # 限制历史长度
                if len(self.conversation_history) > 20:
                    self.conversation_history = self.conversation_history[-20:]
                
                return {"success": True, "response": ai_response}
            else:
                error_msg = f"API 错误: {response.status_code} - {response.text}"
                return {"success": False, "error": error_msg}
                
        except Exception as e:
            return {"success": False, "error": f"请求异常: {e}"}
    
    def clear_history(self):
        """清除对话历史"""
        self.conversation_history = []

# 创建智能体实例
agents = {
    "general": ModelScopeAgent(
        name="通用助手",
        description="我是一个友好的通用 AI 助手",
        instructions=[
            "使用中文回答所有问题",
            "保持友好和专业的语气",
            "提供准确、详细的信息",
            "如果不确定，请诚实说明"
        ]
    ),
    
    "coding": ModelScopeAgent(
        name="编程专家",
        description="我是一个专业的编程助手",
        instructions=[
            "专注于编程和技术问题",
            "提供清晰的代码示例",
            "解释代码的工作原理",
            "使用中文注释"
        ]
    ),
    
    "creative": ModelScopeAgent(
        name="创意写手",
        description="我是一个富有创意的写作助手",
        instructions=[
            "发挥创意和想象力",
            "使用生动有趣的语言",
            "根据需求调整写作风格",
            "保持内容的原创性"
        ]
    ),
    
    "tutor": ModelScopeAgent(
        name="学习导师",
        description="我是一个耐心的学习指导老师",
        instructions=[
            "以教学的方式解释概念",
            "使用简单易懂的语言",
            "提供学习建议和方法",
            "鼓励和支持学习者"
        ]
    )
}

# 创建 FastAPI 应用
app = FastAPI(title="ModelScope Playground", description="基于 ModelScope API 的智能体 Playground")

@app.get("/", response_class=HTMLResponse)
async def home():
    """主页"""
    
    agents_list = "\\n".join([
        f'                <option value="{key}">{agent.name} - {agent.description}</option>'
        for key, agent in agents.items()
    ])
    
    html_content = f"""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>ModelScope Playground</title>
        <style>
            body {{
                font-family: 'Microsoft YaHei', Arial, sans-serif;
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f5f5f5;
            }}
            .header {{
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 30px;
                border-radius: 10px;
                margin-bottom: 30px;
                text-align: center;
            }}
            .container {{
                background: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            }}
            .agent-selector {{
                margin-bottom: 20px;
            }}
            .agent-selector select {{
                width: 100%;
                padding: 12px;
                border: 2px solid #ddd;
                border-radius: 5px;
                font-size: 16px;
            }}
            .chat-area {{
                border: 2px solid #ddd;
                border-radius: 10px;
                height: 400px;
                overflow-y: auto;
                padding: 20px;
                margin-bottom: 20px;
                background-color: #fafafa;
            }}
            .message {{
                margin-bottom: 15px;
                padding: 10px;
                border-radius: 8px;
                max-width: 80%;
            }}
            .user-message {{
                background-color: #e3f2fd;
                margin-left: auto;
                text-align: right;
            }}
            .bot-message {{
                background-color: #f1f8e9;
                margin-right: auto;
            }}
            .input-area {{
                display: flex;
                gap: 10px;
            }}
            .input-area textarea {{
                flex: 1;
                padding: 12px;
                border: 2px solid #ddd;
                border-radius: 5px;
                font-size: 16px;
                resize: vertical;
                min-height: 60px;
            }}
            .btn {{
                padding: 12px 24px;
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                font-size: 16px;
                transition: background-color 0.3s;
            }}
            .btn:hover {{
                background-color: #45a049;
            }}
            .btn:disabled {{
                background-color: #cccccc;
                cursor: not-allowed;
            }}
            .clear-btn {{
                background-color: #ff9800;
                margin-top: 10px;
            }}
            .clear-btn:hover {{
                background-color: #e68900;
            }}
            .loading {{
                display: none;
                color: #666;
                font-style: italic;
            }}
            .info {{
                background-color: #e8f4fd;
                padding: 15px;
                border-radius: 5px;
                margin-bottom: 20px;
                border-left: 4px solid #2196F3;
            }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🤖 ModelScope Playground</h1>
            <p>基于 {Config.MODELSCOPE_MODEL} 的智能体平台</p>
        </div>
        
        <div class="container">
            <div class="info">
                <strong>🚀 API 信息:</strong><br>
                模型: {Config.MODELSCOPE_MODEL}<br>
                端点: {Config.MODELSCOPE_BASE_URL}
            </div>
            
            <div class="agent-selector">
                <label for="agentSelect"><strong>选择智能体:</strong></label>
                <select id="agentSelect" onchange="switchAgent()">
{agents_list}
                </select>
            </div>
            
            <div id="chatArea" class="chat-area">
                <div class="message bot-message">
                    <strong>通用助手:</strong> 你好！我是 ModelScope 智能助手。请选择一个智能体开始对话！
                </div>
            </div>
            
            <div class="input-area">
                <textarea id="messageInput" placeholder="输入您的消息..." onkeydown="handleKeyDown(event)"></textarea>
                <button class="btn" onclick="sendMessage()" id="sendBtn">发送</button>
            </div>
            
            <div class="loading" id="loading">🤔 AI 正在思考中...</div>
            
            <button class="btn clear-btn" onclick="clearChat()">清除对话</button>
        </div>
        
        <script>
            let currentAgent = 'general';
            
            function switchAgent() {{
                const select = document.getElementById('agentSelect');
                currentAgent = select.value;
                
                // 清除聊天并显示欢迎消息
                const chatArea = document.getElementById('chatArea');
                const selectedOption = select.options[select.selectedIndex];
                const agentName = selectedOption.text.split(' - ')[0];
                
                chatArea.innerHTML = `
                    <div class="message bot-message">
                        <strong>${{agentName}}:</strong> 你好！我已切换为当前智能体。有什么可以帮助您的吗？
                    </div>
                `;
                
                // 清除服务器端历史
                fetch('/clear', {{
                    method: 'POST',
                    headers: {{'Content-Type': 'application/json'}},
                    body: JSON.stringify({{'agent': currentAgent}})
                }});
            }}
            
            function sendMessage() {{
                const input = document.getElementById('messageInput');
                const message = input.value.trim();
                
                if (!message) return;
                
                // 添加用户消息到聊天区域
                addMessage(message, 'user');
                
                // 清空输入框并禁用发送按钮
                input.value = '';
                document.getElementById('sendBtn').disabled = true;
                document.getElementById('loading').style.display = 'block';
                
                // 发送请求到服务器
                fetch('/chat', {{
                    method: 'POST',
                    headers: {{'Content-Type': 'application/json'}},
                    body: JSON.stringify({{
                        'message': message,
                        'agent': currentAgent
                    }})
                }})
                .then(response => response.json())
                .then(data => {{
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('sendBtn').disabled = false;
                    
                    if (data.success) {{
                        addMessage(data.response, 'bot');
                    }} else {{
                        addMessage('错误: ' + data.error, 'bot');
                    }}
                }})
                .catch(error => {{
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('sendBtn').disabled = false;
                    addMessage('网络错误: ' + error, 'bot');
                }});
            }}
            
            function addMessage(message, type) {{
                const chatArea = document.getElementById('chatArea');
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${{type === 'user' ? 'user-message' : 'bot-message'}}`;
                
                const agentSelect = document.getElementById('agentSelect');
                const agentName = agentSelect.options[agentSelect.selectedIndex].text.split(' - ')[0];
                const sender = type === 'user' ? '用户' : agentName;
                
                messageDiv.innerHTML = `<strong>${{sender}}:</strong> ${{message.replace(/\\n/g, '<br>')}}`;
                chatArea.appendChild(messageDiv);
                chatArea.scrollTop = chatArea.scrollHeight;
            }}
            
            function clearChat() {{
                const chatArea = document.getElementById('chatArea');
                const agentSelect = document.getElementById('agentSelect');
                const agentName = agentSelect.options[agentSelect.selectedIndex].text.split(' - ')[0];
                
                chatArea.innerHTML = `
                    <div class="message bot-message">
                        <strong>${{agentName}}:</strong> 对话已清除。有什么新的问题吗？
                    </div>
                `;
                
                // 清除服务器端历史
                fetch('/clear', {{
                    method: 'POST',
                    headers: {{'Content-Type': 'application/json'}},
                    body: JSON.stringify({{'agent': currentAgent}})
                }});
            }}
            
            function handleKeyDown(event) {{
                if (event.key === 'Enter' && !event.shiftKey) {{
                    event.preventDefault();
                    sendMessage();
                }}
            }}
            
            // 页面加载完成后设置焦点
            document.addEventListener('DOMContentLoaded', function() {{
                document.getElementById('messageInput').focus();
            }});
        </script>
    </body>
    </html>
    """
    
    return HTMLResponse(content=html_content)

@app.post("/chat")
async def chat(request: Request):
    """处理聊天请求"""
    
    try:
        data = await request.json()
        message = data.get("message", "")
        agent_key = data.get("agent", "general")
        
        if not message:
            return JSONResponse({"success": False, "error": "消息不能为空"})
        
        if agent_key not in agents:
            return JSONResponse({"success": False, "error": "无效的智能体"})
        
        agent = agents[agent_key]
        result = agent.chat(message)
        
        return JSONResponse(result)
        
    except Exception as e:
        return JSONResponse({"success": False, "error": f"服务器错误: {e}"})

@app.post("/clear")
async def clear_chat(request: Request):
    """清除对话历史"""
    
    try:
        data = await request.json()
        agent_key = data.get("agent", "general")
        
        if agent_key in agents:
            agents[agent_key].clear_history()
            return JSONResponse({"success": True})
        else:
            return JSONResponse({"success": False, "error": "无效的智能体"})
            
    except Exception as e:
        return JSONResponse({"success": False, "error": f"服务器错误: {e}"})

@app.get("/api/agents")
async def get_agents():
    """获取所有智能体信息"""
    
    agents_info = {}
    for key, agent in agents.items():
        agents_info[key] = {
            "name": agent.name,
            "description": agent.description,
            "instructions": agent.instructions
        }
    
    return JSONResponse(agents_info)

@app.get("/api/status")
async def get_status():
    """获取系统状态"""
    
    return JSONResponse({
        "status": "running",
        "model": Config.MODELSCOPE_MODEL,
        "api": Config.MODELSCOPE_BASE_URL,
        "agents_count": len(agents),
        "timestamp": datetime.now().isoformat()
    })

def test_connection():
    """测试 API 连接"""
    
    print("=== 测试 ModelScope API 连接 ===\\n")
    
    test_agent = agents["general"]
    result = test_agent.chat("你好")
    
    if result["success"]:
        print("✓ API 连接成功!")
        print(f"测试响应: {result['response'][:100]}...")
        return True
    else:
        print(f"✗ API 连接失败: {result['error']}")
        return False

def main():
    """启动 Playground 服务"""
    
    print("=== ModelScope Playground 启动器 ===\\n")
    
    # 先测试连接
    if not test_connection():
        print("\\nAPI 连接失败，请检查配置后重试")
        return
    
    print(f"\\n✓ 智能体 Playground 准备就绪")
    print(f"✓ 已创建 {len(agents)} 个智能体:")
    for key, agent in agents.items():
        print(f"  - {agent.name}: {agent.description}")
    
    print(f"\\n🚀 正在启动 Web 服务...")
    print(f"📱 访问地址: http://localhost:7777")
    print(f"🛑 按 Ctrl+C 停止服务\\n")
    
    try:
        uvicorn.run(
            app,
            host="localhost",
            port=7777,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()