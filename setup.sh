#!/bin/bash

# Agno 智能体项目初始化脚本
# 此脚本用于快速设置和初始化项目环境

set -e  # 遇到错误时退出

echo "======================================"
echo "    Agno 智能体项目初始化脚本"
echo "======================================"

# 检查 Python 版本
echo "检查 Python 版本..."
python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" = "$required_version" ]; then
    echo "✓ Python 版本 $python_version 满足要求"
else
    echo "✗ Python 版本过低。需要 Python 3.8+，当前版本: $python_version"
    exit 1
fi

# 检查是否在虚拟环境中
if [[ "$VIRTUAL_ENV" != "" ]]; then
    echo "✓ 正在虚拟环境中: $VIRTUAL_ENV"
else
    echo "⚠ 未检测到虚拟环境"
    read -p "是否创建虚拟环境？(y/n): " create_venv
    
    if [[ $create_venv == "y" || $create_venv == "Y" ]]; then
        echo "创建虚拟环境..."
        python3 -m venv agno_env
        echo "激活虚拟环境..."
        source agno_env/bin/activate
        echo "✓ 虚拟环境已创建并激活"
    fi
fi

# 安装依赖
echo "安装项目依赖..."
pip install --upgrade pip
pip install -r requirements.txt

echo "✓ 依赖安装完成"

# 创建环境变量文件
if [ ! -f .env ]; then
    echo "创建环境变量文件..."
    cp .env.example .env
    echo "✓ .env 文件已创建"
    echo "⚠ 请编辑 .env 文件并设置您的 API 密钥"
else
    echo "✓ .env 文件已存在"
fi

# 创建必要目录
echo "创建项目目录..."
mkdir -p data logs

echo "✓ 项目目录创建完成"

# 验证安装
echo "验证安装..."
if python3 -c "import agno; print('Agno 安装成功')" 2>/dev/null; then
    echo "✓ Agno 框架安装成功"
else
    echo "✗ Agno 框架安装失败"
    exit 1
fi

echo ""
echo "======================================"
echo "        初始化完成！"
echo "======================================"
echo ""
echo "下一步操作："
echo "1. 编辑 .env 文件，设置您的 API 密钥"
echo "2. 运行: python main.py"
echo "3. 选择要体验的智能体示例"
echo ""
echo "API 密钥获取："
echo "- OpenAI: https://platform.openai.com/api-keys"
echo "- Anthropic: https://console.anthropic.com/account/keys"
echo ""

# 询问是否立即运行
read -p "是否立即运行主程序？(y/n): " run_main

if [[ $run_main == "y" || $run_main == "Y" ]]; then
    echo "启动 Agno 智能体项目..."
    python3 main.py
fi