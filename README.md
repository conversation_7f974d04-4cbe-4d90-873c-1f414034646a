# Agno ModelScope 智能体项目

基于 Agno 框架和 ModelScope Qwen3-235B-A22B 模型构建的智能体项目，支持多智能体协作和交互式 Playground。

## 🚀 快速开始

### ModelScope 配置

本项目已预配置 ModelScope API：
- **API 端点**: https://api-inference.modelscope.cn/v1
- **模型**: Qwen/Qwen3-235B-A22B  
- **API 密钥**: bd8a6950-d161-4084-abb8-861bf91f8f00

### 一键启动

```bash
# 1. 自动化环境搭建 (使用 uv)
chmod +x setup_uv.sh
./setup_uv.sh

# 2. 启动 ModelScope Playground
source .venv/bin/activate
python modelscope_playground.py

# 或使用主启动器
python main_launcher.py
```

### 🎮 ModelScope Playground 特性

- **4种智能体类型**：通用助手、编程专家、创意写手、学习导师
- **流式响应**：实时显示 AI 生成内容
- **对话历史管理**：自动保存和管理上下文
- **智能体切换**：无缝切换不同专业领域的助手
- **丰富命令**：list、switch、clear、quit 等交互命令

## 项目简介

Agno 是一个轻量级的智能体构建框架，支持创建具备记忆、知识、工具和推理能力的智能体。本项目提供了完整的示例代码和最佳实践，帮助您快速上手 Agno 框架。

## 主要特性

- ⚡ **极速响应**: 智能体平均实例化时间仅 2μs，比 LangGraph 快 10,000 倍
- 🧠 **推理能力**: 支持逐步推理和思考过程展示
- 🔧 **丰富工具**: 内置多种工具，支持自定义工具开发
- 👥 **团队协作**: 支持多智能体团队的三种协作模式
- 🌐 **模型无关**: 统一接口支持 23+ 模型提供商
- 💾 **长期记忆**: 支持会话存储和状态保持

## 项目结构

```
agno_agent/
├── README.md                  # 项目文档
├── requirements.txt           # 项目依赖
├── config.py                 # 配置文件
├── .env.example              # 环境变量示例
├── examples/                 # 示例代码
│   ├── basic_agent.py        # 基础智能体示例
│   ├── agent_with_tools.py   # 带工具的智能体示例
│   ├── reasoning_agent.py    # 推理智能体示例
│   ├── multi_agent_team.py   # 多智能体团队示例
│   └── custom_tools_agent.py # 自定义工具示例
├── tools/                    # 自定义工具
│   ├── __init__.py
│   └── custom_tools.py       # 自定义工具实现
├── agents/                   # 智能体模块（预留）
├── data/                     # 数据目录
└── logs/                     # 日志目录
```

## 快速开始

### 1. 环境准备

```bash
# 克隆或下载项目
cd agno_agent

# 安装依赖
pip install -r requirements.txt

# 复制环境变量文件并配置 API 密钥
cp .env.example .env
# 编辑 .env 文件，设置您的 OpenAI 和 Anthropic API 密钥
```

### 2. 配置 API 密钥

在 `.env` 文件中设置您的 API 密钥：

```bash
# OpenAI API Key
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic API Key (Claude)
ANTHROPIC_API_KEY=your_anthropic_api_key_here
```

### 3. 运行示例

#### 基础智能体
```bash
python examples/basic_agent.py
```

#### 带工具的智能体
```bash
python examples/agent_with_tools.py
```

#### 推理智能体
```bash
python examples/reasoning_agent.py
```

#### 多智能体团队
```bash
python examples/multi_agent_team.py
```

#### 自定义工具智能体
```bash
python examples/custom_tools_agent.py
```

## 智能体类型

### 1. 基础智能体
- **功能**: 基本对话能力
- **适用场景**: 简单问答、文本生成
- **特点**: 轻量级、快速响应

### 2. 工具智能体
- **功能**: 集成各种工具（搜索、数据获取等）
- **适用场景**: 信息查询、数据分析
- **包含工具**: 
  - DuckDuckGo 搜索
  - Yahoo Finance 金融数据
  - 自定义工具

### 3. 推理智能体
- **功能**: 逐步推理和分析
- **适用场景**: 复杂分析、决策支持
- **特点**: 展示思考过程、逻辑清晰

### 4. 多智能体团队
- **功能**: 多个智能体协作完成任务
- **协作模式**:
  - **Route**: 路由模式，根据问题类型选择专家
  - **Collaborate**: 协作模式，所有成员共同参与
  - **Coordinate**: 协调模式，由协调员分配任务

## 自定义工具

项目提供了自定义工具的示例，包括：

- **天气工具**: 查询城市天气信息
- **计算器工具**: 数学计算和单位转换
- **文本处理工具**: 文本分析和格式化
- **时间工具**: 时间查询和计算

### 创建自定义工具

```python
from agno.tools import Tool

class CustomTool(Tool):
    def __init__(self):
        super().__init__(
            name="custom_tool",
            description="工具描述"
        )
    
    def custom_function(self, param: str) -> str:
        """自定义功能实现"""
        return f"处理结果: {param}"
```

## 配置说明

### 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `OPENAI_API_KEY` | OpenAI API 密钥 | 必需 |
| `ANTHROPIC_API_KEY` | Anthropic API 密钥 | 必需 |
| `AGNO_TELEMETRY` | 是否启用遥测 | true |
| `LOG_LEVEL` | 日志级别 | INFO |
| `VECTOR_DB_PATH` | 向量数据库路径 | ./data/vectordb |

### 模型配置

项目支持多种模型：

- **OpenAI**: GPT-4o, GPT-3.5-turbo 等
- **Anthropic**: Claude-3.5-sonnet, Claude-3-haiku 等

## 使用技巧

### 1. 性能优化
- Agno 具有极快的实例化速度（平均 2μs）
- 内存占用极小（平均 3.75KB）
- 适合高并发场景

### 2. 推理功能
- 使用 `show_full_reasoning=True` 展示完整推理过程
- 使用 `stream_intermediate_steps=True` 流式显示中间步骤

### 3. 团队协作
- 选择合适的协作模式（route/collaborate/coordinate）
- 为每个智能体定义清晰的角色和职责
- 设置明确的成功标准

### 4. 工具使用
- 工具调用会自动显示（`show_tool_calls=True`）
- 可以组合多个工具以增强功能
- 自定义工具应该有清晰的文档字符串

## 故障排除

### 常见问题

1. **API 密钥未设置**
   - 检查 `.env` 文件是否存在
   - 确认 API 密钥格式正确

2. **依赖安装失败**
   - 使用 Python 3.8+ 版本
   - 考虑使用虚拟环境

3. **工具调用失败**
   - 检查网络连接
   - 确认工具权限设置

### 调试技巧

- 设置 `LOG_LEVEL=DEBUG` 获取详细日志
- 使用 `show_tool_calls=True` 查看工具调用过程
- 检查 `./logs/` 目录下的日志文件

## 参与贡献

欢迎提交 Issue 和 Pull Request！

### 开发流程

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。

## 参考资源

- [Agno 官方文档](https://docs.agno.com)
- [Agno GitHub 仓库](https://github.com/agno-agi/agno)
- [Agno 示例库](https://github.com/agno-agi/agno/tree/main/cookbook)

## 更新日志

### v1.0.0
- 基础智能体示例
- 工具集成示例
- 推理智能体示例
- 多智能体团队示例
- 自定义工具示例
- 完整文档和配置

---

如有问题或建议，请提交 Issue 或联系项目维护者。