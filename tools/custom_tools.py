"""
自定义工具示例

这个文件展示如何为 Agno 智能体创建自定义工具。
"""

from agno.tools import Tool
from typing import Optional
import requests
import json
import datetime

class WeatherTool(Tool):
    """天气查询工具示例"""
    
    def __init__(self):
        super().__init__(
            name="weather_tool",
            description="获取指定城市的天气信息"
        )
    
    def get_weather(self, city: str) -> str:
        """
        获取城市天气信息
        
        Args:
            city: 城市名称（中文或英文）
            
        Returns:
            天气信息字符串
        """
        try:
            # 这里使用免费的天气API示例（实际使用时需要注册API key）
            # 这是一个模拟实现
            weather_data = {
                "北京": {"temperature": "15°C", "condition": "晴朗", "humidity": "45%"},
                "上海": {"temperature": "18°C", "condition": "多云", "humidity": "60%"},
                "广州": {"temperature": "25°C", "condition": "小雨", "humidity": "75%"},
                "深圳": {"temperature": "24°C", "condition": "阴天", "humidity": "70%"},
            }
            
            if city in weather_data:
                data = weather_data[city]
                return f"{city}当前天气：温度 {data['temperature']}，天气状况 {data['condition']}，湿度 {data['humidity']}"
            else:
                return f"抱歉，暂时无法获取 {city} 的天气信息"
                
        except Exception as e:
            return f"获取天气信息时发生错误: {str(e)}"

class CalculatorTool(Tool):
    """计算器工具示例"""
    
    def __init__(self):
        super().__init__(
            name="calculator_tool",
            description="执行基本的数学计算"
        )
    
    def calculate(self, expression: str) -> str:
        """
        计算数学表达式
        
        Args:
            expression: 数学表达式（如："2+3*4"）
            
        Returns:
            计算结果
        """
        try:
            # 安全地计算数学表达式
            # 注意：实际应用中应该使用更安全的表达式解析器
            allowed_chars = set('0123456789+-*/.() ')
            if not all(c in allowed_chars for c in expression):
                return "错误：表达式包含不允许的字符"
            
            result = eval(expression)
            return f"{expression} = {result}"
            
        except ZeroDivisionError:
            return "错误：除零错误"
        except Exception as e:
            return f"计算错误: {str(e)}"
    
    def convert_units(self, value: float, from_unit: str, to_unit: str) -> str:
        """
        单位转换
        
        Args:
            value: 数值
            from_unit: 源单位
            to_unit: 目标单位
            
        Returns:
            转换结果
        """
        # 长度转换（米为基准）
        length_units = {
            "mm": 0.001, "cm": 0.01, "m": 1, "km": 1000,
            "inch": 0.0254, "ft": 0.3048, "yard": 0.9144, "mile": 1609.34
        }
        
        # 重量转换（克为基准）
        weight_units = {
            "mg": 0.001, "g": 1, "kg": 1000, "ton": 1000000,
            "oz": 28.3495, "lb": 453.592
        }
        
        try:
            if from_unit in length_units and to_unit in length_units:
                # 长度转换
                meters = value * length_units[from_unit]
                result = meters / length_units[to_unit]
                return f"{value} {from_unit} = {result:.6f} {to_unit}"
            
            elif from_unit in weight_units and to_unit in weight_units:
                # 重量转换
                grams = value * weight_units[from_unit]
                result = grams / weight_units[to_unit]
                return f"{value} {from_unit} = {result:.6f} {to_unit}"
            
            else:
                return f"不支持从 {from_unit} 到 {to_unit} 的转换"
                
        except Exception as e:
            return f"单位转换错误: {str(e)}"

class TextProcessingTool(Tool):
    """文本处理工具示例"""
    
    def __init__(self):
        super().__init__(
            name="text_processing_tool",
            description="提供各种文本处理功能"
        )
    
    def analyze_text(self, text: str) -> str:
        """
        分析文本的基本统计信息
        
        Args:
            text: 要分析的文本
            
        Returns:
            文本分析结果
        """
        try:
            char_count = len(text)
            char_count_no_spaces = len(text.replace(' ', ''))
            word_count = len(text.split())
            line_count = len(text.split('\n'))
            
            # 计算中文字符数量
            chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
            
            result = f"""文本分析结果：
- 总字符数：{char_count}
- 字符数（不含空格）：{char_count_no_spaces}
- 单词数：{word_count}
- 行数：{line_count}
- 中文字符数：{chinese_chars}"""
            
            return result
            
        except Exception as e:
            return f"文本分析错误: {str(e)}"
    
    def format_text(self, text: str, format_type: str) -> str:
        """
        格式化文本
        
        Args:
            text: 要格式化的文本
            format_type: 格式化类型（upper, lower, title, capitalize）
            
        Returns:
            格式化后的文本
        """
        try:
            if format_type == "upper":
                return text.upper()
            elif format_type == "lower":
                return text.lower()
            elif format_type == "title":
                return text.title()
            elif format_type == "capitalize":
                return text.capitalize()
            else:
                return f"不支持的格式化类型: {format_type}"
                
        except Exception as e:
            return f"文本格式化错误: {str(e)}"

class TimeUtilTool(Tool):
    """时间工具示例"""
    
    def __init__(self):
        super().__init__(
            name="time_util_tool",
            description="提供时间相关的实用功能"
        )
    
    def get_current_time(self, timezone: str = "Asia/Shanghai") -> str:
        """
        获取当前时间
        
        Args:
            timezone: 时区（默认为上海时区）
            
        Returns:
            当前时间字符串
        """
        try:
            now = datetime.datetime.now()
            return f"当前时间：{now.strftime('%Y-%m-%d %H:%M:%S')}"
            
        except Exception as e:
            return f"获取时间错误: {str(e)}"
    
    def calculate_time_difference(self, date1: str, date2: str) -> str:
        """
        计算两个日期之间的时间差
        
        Args:
            date1: 第一个日期（格式：YYYY-MM-DD）
            date2: 第二个日期（格式：YYYY-MM-DD）
            
        Returns:
            时间差信息
        """
        try:
            d1 = datetime.datetime.strptime(date1, '%Y-%m-%d')
            d2 = datetime.datetime.strptime(date2, '%Y-%m-%d')
            
            diff = abs((d2 - d1).days)
            
            return f"{date1} 和 {date2} 相差 {diff} 天"
            
        except Exception as e:
            return f"时间计算错误: {str(e)}"

# 工具实例化函数
def get_custom_tools():
    """获取所有自定义工具实例"""
    return [
        WeatherTool(),
        CalculatorTool(),
        TextProcessingTool(),
        TimeUtilTool()
    ]