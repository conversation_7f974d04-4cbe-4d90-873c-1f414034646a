"""
Agno Playground 测试脚本

使用 Agno 的 Playground 功能来测试和交互 ModelScope 智能体
"""

from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.playground import Playground
from config import Config
import logging

# 设置日志
logging.basicConfig(level=getattr(logging, Config.LOG_LEVEL))
logger = logging.getLogger(__name__)

def create_modelscope_agents():
    """创建多个 ModelScope 智能体用于 Playground 测试"""
    
    if not Config.MODELSCOPE_API_KEY:
        print("错误: ModelScope API 密钥未配置")
        return []
    
    # 基础模型配置
    base_model = OpenAIChat(
        id=Config.MODELSCOPE_MODEL,
        api_key=Config.MODELSCOPE_API_KEY,
        base_url=Config.MODELSCOPE_BASE_URL
    )
    
    # 创建不同类型的智能体
    agents = []
    
    # 1. 通用助手
    general_agent = Agent(
        model=base_model,
        name="通用助手",
        description="我是一个通用的AI助手，可以帮助您解答各种问题。",
        instructions=[
            "使用中文回答",
            "保持友好和专业",
            "提供详细和准确的信息"
        ],
        markdown=True
    )
    agents.append(general_agent)
    
    # 2. 编程助手
    coding_agent = Agent(
        model=base_model,
        name="编程助手", 
        description="我是一个专业的编程助手，擅长各种编程语言和技术。",
        instructions=[
            "专注于编程相关问题",
            "提供清晰的代码示例",
            "解释代码的工作原理",
            "使用中文注释和说明"
        ],
        markdown=True
    )
    agents.append(coding_agent)
    
    # 3. 创意写作助手
    creative_agent = Agent(
        model=base_model,
        name="创意写作助手",
        description="我是一个创意写作助手，可以帮助您进行各种创意写作。",
        instructions=[
            "发挥创意和想象力",
            "使用生动的语言",
            "根据用户需求调整写作风格",
            "使用中文创作"
        ],
        markdown=True
    )
    agents.append(creative_agent)
    
    # 4. 学习导师
    tutor_agent = Agent(
        model=base_model,
        name="学习导师",
        description="我是一个学习导师，可以帮助您学习各种知识和技能。",
        instructions=[
            "以教学的方式解释概念",
            "使用简单易懂的语言",
            "提供学习建议和资源",
            "鼓励和支持学习者"
        ],
        markdown=True
    )
    agents.append(tutor_agent)
    
    return agents

def setup_playground():
    """设置 Agno Playground"""
    
    print("=== 初始化 Agno Playground ===\n")
    
    # 创建智能体
    agents = create_modelscope_agents()
    if not agents:
        print("无法创建智能体，请检查配置")
        return None
    
    print(f"✓ 成功创建 {len(agents)} 个智能体")
    for i, agent in enumerate(agents, 1):
        print(f"  {i}. {agent.name}")
    
    try:
        # 创建 Playground
        playground = Playground(
            agents=agents,
            name="ModelScope Agno Playground",
            description="基于 ModelScope Qwen3 模型的智能体测试平台"
        )
        
        print("\n✓ Playground 初始化成功")
        return playground
        
    except Exception as e:
        print(f"✗ Playground 初始化失败: {e}")
        return None

def test_agents_individually():
    """单独测试每个智能体"""
    
    print("=== 单独测试智能体 ===\n")
    
    agents = create_modelscope_agents()
    if not agents:
        return
    
    test_prompts = {
        "通用助手": "请介绍一下人工智能的发展历史",
        "编程助手": "请写一个Python函数来计算两个数的最大公约数",
        "创意写作助手": "请写一个关于未来城市的短故事",
        "学习导师": "如何学习机器学习？请给出学习路径建议"
    }
    
    for agent in agents:
        print(f"{'='*60}")
        print(f"测试智能体: {agent.name}")
        print(f"{'='*60}")
        
        prompt = test_prompts.get(agent.name, "你好，请介绍一下自己")
        print(f"测试问题: {prompt}\n")
        
        try:
            agent.print_response(prompt, stream=True)
            print("\n✓ 测试成功")
        except Exception as e:
            print(f"✗ 测试失败: {e}")
        
        print("\n")

def interactive_playground():
    """交互式 Playground 模式"""
    
    playground = setup_playground()
    if not playground:
        return
    
    print("\n=== Agno Playground 交互模式 ===")
    print("您可以与不同的智能体进行对话")
    print("输入 'list' 查看可用智能体")
    print("输入 'switch <number>' 切换智能体")
    print("输入 'quit' 或 'exit' 退出\n")
    
    current_agent_index = 0
    agents = playground.agents
    current_agent = agents[current_agent_index]
    
    print(f"当前智能体: {current_agent.name}")
    print(f"描述: {current_agent.description}\n")
    
    while True:
        try:
            user_input = input("用户: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("感谢使用 Agno Playground！")
                break
            
            elif user_input.lower() == 'list':
                print("\n可用的智能体:")
                for i, agent in enumerate(agents):
                    marker = "→" if i == current_agent_index else " "
                    print(f"  {marker} {i+1}. {agent.name} - {agent.description}")
                print()
                continue
            
            elif user_input.lower().startswith('switch '):
                try:
                    agent_num = int(user_input.split()[1]) - 1
                    if 0 <= agent_num < len(agents):
                        current_agent_index = agent_num
                        current_agent = agents[current_agent_index]
                        print(f"\n已切换到: {current_agent.name}")
                        print(f"描述: {current_agent.description}\n")
                    else:
                        print("无效的智能体编号\n")
                except (IndexError, ValueError):
                    print("请输入有效的智能体编号，例如: switch 2\n")
                continue
            
            elif not user_input:
                continue
            
            # 与当前智能体对话
            print(f"\n{current_agent.name}:")
            current_agent.print_response(user_input, stream=True)
            print("\n" + "="*50 + "\n")
            
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            break
        except Exception as e:
            logger.error(f"发生错误: {e}")
            print(f"发生错误: {e}\n")

def benchmark_test():
    """性能测试"""
    
    print("=== ModelScope 智能体性能测试 ===\n")
    
    agents = create_modelscope_agents()
    if not agents:
        return
    
    import time
    
    test_question = "请用一句话介绍人工智能"
    
    for agent in agents:
        print(f"测试 {agent.name}...")
        
        start_time = time.time()
        try:
            response = agent.run(test_question)
            end_time = time.time()
            
            response_time = end_time - start_time
            response_length = len(response.content) if hasattr(response, 'content') else len(str(response))
            
            print(f"  ✓ 响应时间: {response_time:.2f}秒")
            print(f"  ✓ 响应长度: {response_length}字符")
            print(f"  ✓ 响应内容: {str(response)[:100]}...")
            
        except Exception as e:
            print(f"  ✗ 测试失败: {e}")
        
        print()

def main():
    """主函数"""
    
    print("=== ModelScope Agno Playground ===\n")
    print(f"模型: {Config.MODELSCOPE_MODEL}")
    print(f"API 端点: {Config.MODELSCOPE_BASE_URL}")
    print()
    
    print("选择测试模式:")
    print("1. 交互式 Playground")
    print("2. 单独测试智能体")
    print("3. 性能基准测试")
    print("4. 退出")
    
    try:
        choice = input("\n请选择 (1-4): ").strip()
        
        if choice == "1":
            interactive_playground()
        elif choice == "2":
            test_agents_individually()
        elif choice == "3":
            benchmark_test()
        elif choice == "4":
            print("退出")
        else:
            print("无效选择，启动交互式 Playground")
            interactive_playground()
    
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序发生错误: {e}")

if __name__ == "__main__":
    main()