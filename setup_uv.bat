@echo off
REM Agno 智能体项目初始化脚本 (使用 uv - Windows)

echo ======================================
echo   Agno 智能体项目初始化脚本 (uv)
echo ======================================

REM 检查 uv 是否已安装
uv --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ✗ uv 未安装
    echo 正在安装 uv...
    
    REM 使用 PowerShell 安装 uv
    powershell -Command "& {irm https://astral.sh/uv/install.ps1 | iex}"
    
    REM 验证安装
    uv --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo ✗ uv 安装失败，请手动安装
        echo 访问: https://github.com/astral-sh/uv
        pause
        exit /b 1
    )
)

for /f "tokens=*" %%i in ('uv --version') do set uv_version=%%i
echo ✓ uv 已安装: %uv_version%

REM 检查 Python 版本
echo 检查 Python 版本...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ✗ Python 未安装或未添加到 PATH
    echo 请访问 https://python.org 下载并安装 Python 3.8+
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set python_version=%%i
echo ✓ Python 版本: %python_version%

REM 创建虚拟环境
echo 创建虚拟环境...
if exist ".venv" (
    echo ⚠ 虚拟环境已存在，正在重新创建...
    rmdir /s /q .venv
)

uv venv --python python
if %errorlevel% neq 0 (
    echo ✗ 虚拟环境创建失败
    pause
    exit /b 1
)
echo ✓ 虚拟环境创建完成

REM 激活虚拟环境
echo 激活虚拟环境...
call .venv\Scripts\activate.bat
echo ✓ 虚拟环境已激活

REM 配置国内镜像源
echo 配置国内镜像源...
set UV_INDEX_URL=https://pypi.tuna.tsinghua.edu.cn/simple
set UV_EXTRA_INDEX_URL=https://mirrors.aliyun.com/pypi/simple
echo ✓ 镜像源配置完成

REM 安装依赖
echo 安装项目依赖...
echo 使用镜像源: %UV_INDEX_URL%

REM 先安装核心依赖
uv pip install agno openai python-dotenv

REM 安装所有依赖
if exist "pyproject.toml" (
    echo 从 pyproject.toml 安装依赖...
    uv pip install -e .
) else (
    echo 从 requirements.txt 安装依赖...
    uv pip install -r requirements.txt
)

if %errorlevel% neq 0 (
    echo ✗ 依赖安装失败
    pause
    exit /b 1
)

echo ✓ 依赖安装完成

REM 创建环境变量文件
if not exist .env (
    echo 创建环境变量文件...
    copy .env.example .env >nul
    echo ✓ .env 文件已创建
    echo ⚠ 请编辑 .env 文件并设置您的 API 密钥
) else (
    echo ✓ .env 文件已存在
)

REM 创建必要目录
echo 创建项目目录...
if not exist data mkdir data
if not exist logs mkdir logs
if not exist .uv-cache mkdir .uv-cache

echo ✓ 项目目录创建完成

REM 验证安装
echo 验证安装...
.venv\Scripts\python.exe -c "import agno; print('✓ Agno 安装成功')" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ 核心依赖验证成功
) else (
    echo ✗ 核心依赖验证失败
    pause
    exit /b 1
)

echo.
echo ======================================
echo         初始化完成！
echo ======================================
echo.
echo 虚拟环境信息：
echo - 位置: %cd%\.venv
for /f "tokens=*" %%i in ('.venv\Scripts\python.exe --version') do echo - Python: %%i
for /f "tokens=*" %%i in ('uv --version') do echo - uv 版本: %%i
echo.
echo 使用说明：
echo 1. 激活虚拟环境: .venv\Scripts\activate.bat
echo 2. 编辑 .env 文件设置 API 密钥
echo 3. 运行测试: python examples\modelscope_agent.py
echo 4. 使用 Playground: python playground_test.py
echo.
echo 依赖管理：
echo - 添加依赖: uv pip install package_name
echo - 列出依赖: uv pip list
echo - 导出依赖: uv pip freeze ^> requirements.txt
echo.

REM 询问是否立即运行测试
set /p test_agent="是否立即测试 ModelScope 智能体？(y/n): "

if /i "%test_agent%"=="y" (
    echo 启动 ModelScope 智能体测试...
    .venv\Scripts\python.exe examples\modelscope_agent.py
)

pause