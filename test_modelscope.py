#!/usr/bin/env python3
"""
简化的 ModelScope 智能体测试

直接使用 OpenAI 库测试 ModelScope API 连接
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from openai import OpenAI
from config import Config
import json

def test_modelscope_direct():
    """直接测试 ModelScope API"""
    
    print("=== 直接测试 ModelScope API ===\n")
    print(f"API 端点: {Config.MODELSCOPE_BASE_URL}")
    print(f"模型: {Config.MODELSCOPE_MODEL}")
    print(f"API 密钥: {Config.MODELSCOPE_API_KEY[:20]}...")
    
    try:
        # 创建 OpenAI 客户端，指向 ModelScope
        client = OpenAI(
            api_key=Config.MODELSCOPE_API_KEY,
            base_url=Config.MODELSCOPE_BASE_URL
        )
        
        # 测试简单的聊天请求
        print("\n正在发送测试请求...")
        response = client.chat.completions.create(
            model=Config.MODELSCOPE_MODEL,
            messages=[
                {"role": "user", "content": "你好，请简单介绍一下自己"}
            ],
            max_tokens=200,
            temperature=0.7,
            enable_thinking=False  # ModelScope 特殊要求
        )
        
        print("✓ API 连接成功！")
        print(f"响应: {response.choices[0].message.content}")
        return True
        
    except Exception as e:
        print(f"✗ API 连接失败: {e}")
        return False

def test_agno_with_simple_config():
    """使用简化配置测试 Agno"""
    
    print("\n=== 测试 Agno 智能体 ===\n")
    
    try:
        from agno.agent import Agent
        from agno.models.openai import OpenAIChat
        
        # 创建模型，不使用任何特殊的系统消息
        model = OpenAIChat(
            id=Config.MODELSCOPE_MODEL,
            api_key=Config.MODELSCOPE_API_KEY,
            base_url=Config.MODELSCOPE_BASE_URL,
        )
        
        # 创建最简单的智能体
        agent = Agent(
            model=model,
            instructions=["请用中文回答"],
            markdown=False,
            show_tool_calls=False
        )
        
        print("正在测试 Agno 智能体...")
        response = agent.run("你好，请用一句话介绍自己")
        print(f"✓ Agno 智能体测试成功！")
        print(f"响应: {response.content}")
        return True
        
    except Exception as e:
        print(f"✗ Agno 智能体测试失败: {e}")
        return False

def interactive_chat():
    """简单的交互聊天"""
    
    print("\n=== ModelScope 交互聊天 ===\n")
    
    try:
        client = OpenAI(
            api_key=Config.MODELSCOPE_API_KEY,
            base_url=Config.MODELSCOPE_BASE_URL
        )
        
        print("ModelScope 聊天机器人已启动！")
        print("输入 'quit' 退出聊天。\n")
        
        messages = []
        
        while True:
            user_input = input("用户: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("聊天结束，再见！")
                break
            
            if not user_input:
                continue
            
            messages.append({"role": "user", "content": user_input})
            
            try:
                print("AI 正在思考中...")
                response = client.chat.completions.create(
                    model=Config.MODELSCOPE_MODEL,
                    messages=messages,
                    max_tokens=1000,
                    temperature=0.7,
                    stream=True,  # 启用流式响应
                    enable_thinking=False  # ModelScope 特殊要求
                )
                
                print("AI: ", end="", flush=True)
                full_response = ""
                
                for chunk in response:
                    if chunk.choices[0].delta.content:
                        content = chunk.choices[0].delta.content
                        print(content, end="", flush=True)
                        full_response += content
                
                print()  # 换行
                messages.append({"role": "assistant", "content": full_response})
                print()
                
            except Exception as e:
                print(f"发生错误: {e}")
                messages.pop()  # 移除失败的用户消息
    
    except Exception as e:
        print(f"无法启动聊天: {e}")

def main():
    """主函数"""
    
    print("=== ModelScope API 测试工具 ===\n")
    
    # 首先测试直接连接
    if not test_modelscope_direct():
        print("\n直接 API 测试失败，请检查配置")
        return
    
    # 测试 Agno 集成
    agno_success = test_agno_with_simple_config()
    
    if agno_success:
        print("\n✓ 所有测试通过！ModelScope 集成正常工作")
    else:
        print("\n⚠ Agno 集成有问题，但直接 API 调用正常")
    
    # 询问是否进入交互模式
    choice = input("\n是否进入交互聊天模式？(y/n): ").strip().lower()
    if choice == 'y':
        interactive_chat()

if __name__ == "__main__":
    main()