"""
基础智能体示例

这个示例展示如何创建一个简单的 Agno 智能体，具备基本的对话能力。
"""

from agno.agent import Agent
from agno.models.openai import OpenAIChat
from config import Config
import logging

# 设置日志
logging.basicConfig(level=getattr(logging, Config.LOG_LEVEL))
logger = logging.getLogger(__name__)

def create_basic_agent():
    """创建一个基础智能体"""
    
    # 验证 API 密钥
    if not Config.validate_api_keys():
        return None
    
    agent = Agent(
        model=OpenAIChat(id=Config.DEFAULT_OPENAI_MODEL),
        description="你是一个友好且有帮助的AI助手，擅长回答各种问题并提供有用的建议。",
        instructions=[
            "请用中文回答所有问题",
            "保持友好和专业的语气",
            "如果不确定答案，请诚实地说明",
            "尽量提供详细和有用的信息"
        ],
        markdown=True,
        show_tool_calls=True
    )
    
    return agent

def main():
    """主函数：演示基础智能体的使用"""
    print("=== Agno 基础智能体示例 ===\n")
    
    # 创建智能体
    agent = create_basic_agent()
    if not agent:
        print("无法创建智能体，请检查配置")
        return
    
    print("智能体已成功创建！")
    print("你可以开始与智能体对话了。输入 'quit' 或 'exit' 退出。\n")
    
    # 交互循环
    while True:
        try:
            user_input = input("用户: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出', '结束']:
                print("再见！")
                break
            
            if not user_input:
                continue
                
            print("\n智能体:")
            agent.print_response(user_input, stream=True)
            print("\n" + "="*50 + "\n")
            
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            break
        except Exception as e:
            logger.error(f"发生错误: {e}")
            print(f"发生错误: {e}")

if __name__ == "__main__":
    main()