"""
多智能体团队协作示例

这个示例展示如何创建多智能体团队，实现协作完成复杂任务。
Agno 支持三种协作模式：route（路由）、collaborate（协作）和 coordinate（协调）。
"""

from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.models.anthropic import Claude
from agno.tools.duckduckgo import DuckDuckGoTools
from agno.tools.yfinance import YFinanceTools
from agno.team import Team
from config import Config
import logging

# 设置日志
logging.basicConfig(level=getattr(logging, Config.LOG_LEVEL))
logger = logging.getLogger(__name__)

def create_research_team():
    """创建研究团队 - 使用 coordinate 模式"""
    
    if not Config.validate_api_keys():
        return None
    
    # 网络搜索专家
    web_agent = Agent(
        name="网络搜索专家",
        role="搜索网络信息并提供最新资讯",
        model=OpenAIChat(id=Config.DEFAULT_OPENAI_MODEL),
        tools=[DuckDuckGoTools()],
        instructions=[
            "专注于搜索最新、准确的网络信息",
            "始终包含信息来源",
            "使用中文回答"
        ],
        show_tool_calls=True,
        markdown=True
    )
    
    # 金融分析专家
    finance_agent = Agent(
        name="金融分析专家",
        role="获取金融数据并进行分析",
        model=OpenAIChat(id=Config.DEFAULT_OPENAI_MODEL),
        tools=[YFinanceTools(
            stock_price=True,
            analyst_recommendations=True,
            company_info=True,
            company_news=True
        )],
        instructions=[
            "专注于金融数据分析",
            "使用表格展示数据",
            "提供专业的投资建议",
            "使用中文回答"
        ],
        show_tool_calls=True,
        markdown=True
    )
    
    # 创建团队
    research_team = Team(
        mode="coordinate",  # 协调模式
        members=[web_agent, finance_agent],
        model=OpenAIChat(id=Config.DEFAULT_OPENAI_MODEL),
        name="市场研究团队",
        description="专业的市场研究和分析团队",
        success_criteria="提供全面的市场分析报告，包含清晰的章节和数据驱动的洞察",
        instructions=[
            "始终包含信息来源",
            "使用表格展示数据",
            "使用中文撰写报告",
            "确保报告的完整性和专业性"
        ],
        show_tool_calls=True,
        markdown=True
    )
    
    return research_team

def create_content_team():
    """创建内容创作团队 - 使用 collaborate 模式"""
    
    if not Config.validate_api_keys():
        return None
    
    # 内容研究员
    researcher = Agent(
        name="内容研究员",
        role="收集和整理相关信息",
        model=OpenAIChat(id=Config.DEFAULT_OPENAI_MODEL),
        tools=[DuckDuckGoTools()],
        instructions=[
            "深入研究主题相关信息",
            "收集多方面的观点和数据",
            "使用中文进行研究"
        ],
        show_tool_calls=True,
        markdown=True
    )
    
    # 内容创作者
    writer = Agent(
        name="内容创作者",
        role="基于研究信息创作优质内容",
        model=Claude(id=Config.DEFAULT_CLAUDE_MODEL) if Config.ANTHROPIC_API_KEY else OpenAIChat(id=Config.DEFAULT_OPENAI_MODEL),
        instructions=[
            "创作引人入胜的内容",
            "确保内容结构清晰",
            "使用生动的语言",
            "使用中文写作"
        ],
        markdown=True
    )
    
    # 质量审核员
    reviewer = Agent(
        name="质量审核员",
        role="审核内容质量并提供改进建议",
        model=OpenAIChat(id=Config.DEFAULT_OPENAI_MODEL),
        instructions=[
            "仔细审核内容质量",
            "检查事实准确性",
            "提供建设性的改进建议",
            "使用中文进行审核"
        ],
        markdown=True
    )
    
    # 创建团队
    content_team = Team(
        mode="collaborate",  # 协作模式
        members=[researcher, writer, reviewer],
        model=OpenAIChat(id=Config.DEFAULT_OPENAI_MODEL),
        name="内容创作团队",
        description="专业的内容创作和质量保证团队",
        success_criteria="创作高质量、准确且引人入胜的内容",
        instructions=[
            "确保内容的准确性和可读性",
            "使用中文创作",
            "遵循专业的写作标准"
        ],
        show_tool_calls=True,
        markdown=True
    )
    
    return content_team

def create_consulting_team():
    """创建咨询团队 - 使用 route 模式"""
    
    if not Config.validate_api_keys():
        return None
    
    # 技术顾问
    tech_consultant = Agent(
        name="技术顾问",
        role="提供技术相关的咨询和建议",
        model=OpenAIChat(id=Config.DEFAULT_OPENAI_MODEL),
        tools=[DuckDuckGoTools()],
        instructions=[
            "专注于技术问题和解决方案",
            "提供实用的技术建议",
            "使用中文回答"
        ],
        show_tool_calls=True,
        markdown=True
    )
    
    # 商业顾问
    business_consultant = Agent(
        name="商业顾问",
        role="提供商业策略和市场分析",
        model=OpenAIChat(id=Config.DEFAULT_OPENAI_MODEL),
        tools=[DuckDuckGoTools(), YFinanceTools(
            stock_price=True,
            company_info=True
        )],
        instructions=[
            "专注于商业策略和市场分析",
            "提供可操作的商业建议",
            "使用中文回答"
        ],
        show_tool_calls=True,
        markdown=True
    )
    
    # 法律顾问
    legal_consultant = Agent(
        name="法律顾问",
        role="提供法律和合规相关的建议",
        model=OpenAIChat(id=Config.DEFAULT_OPENAI_MODEL),
        tools=[DuckDuckGoTools()],
        instructions=[
            "专注于法律和合规问题",
            "提供专业的法律建议",
            "强调风险管理",
            "使用中文回答"
        ],
        show_tool_calls=True,
        markdown=True
    )
    
    # 创建团队
    consulting_team = Team(
        mode="route",  # 路由模式
        members=[tech_consultant, business_consultant, legal_consultant],
        model=OpenAIChat(id=Config.DEFAULT_OPENAI_MODEL),
        name="专业咨询团队",
        description="提供技术、商业和法律方面的专业咨询",
        success_criteria="根据问题类型，提供最相关和专业的咨询建议",
        instructions=[
            "根据问题性质选择最合适的专家",
            "提供专业、准确的建议",
            "使用中文回答"
        ],
        show_tool_calls=True,
        markdown=True
    )
    
    return consulting_team

def demo_research_team():
    """演示研究团队的协作"""
    print("=== 市场研究团队演示 ===\n")
    
    team = create_research_team()
    if not team:
        print("无法创建研究团队")
        return
    
    research_task = "分析人工智能半导体公司的市场前景和财务表现"
    
    print(f"研究任务: {research_task}")
    print("="*80)
    
    try:
        team.print_response(research_task, stream=True)
    except Exception as e:
        logger.error(f"团队协作时发生错误: {e}")
        print(f"团队协作时发生错误: {e}")

def demo_content_team():
    """演示内容创作团队的协作"""
    print("=== 内容创作团队演示 ===\n")
    
    team = create_content_team()
    if not team:
        print("无法创建内容团队")
        return
    
    content_task = "创作一篇关于可持续能源技术发展趋势的深度文章"
    
    print(f"创作任务: {content_task}")
    print("="*80)
    
    try:
        team.print_response(content_task, stream=True)
    except Exception as e:
        logger.error(f"团队协作时发生错误: {e}")
        print(f"团队协作时发生错误: {e}")

def demo_consulting_team():
    """演示咨询团队的路由功能"""
    print("=== 专业咨询团队演示 ===\n")
    
    team = create_consulting_team()
    if not team:
        print("无法创建咨询团队")
        return
    
    consulting_questions = [
        "我想开发一个AI应用，需要考虑哪些技术架构问题？",
        "初创公司如何制定有效的市场进入策略？",
        "公司使用AI技术时需要注意哪些法律和隐私问题？"
    ]
    
    print("将演示以下咨询问题:")
    for i, question in enumerate(consulting_questions, 1):
        print(f"{i}. {question}")
    
    print("\n开始咨询演示...\n")
    
    for i, question in enumerate(consulting_questions, 1):
        print(f"{'='*80}")
        print(f"咨询问题 {i}: {question}")
        print(f"{'='*80}")
        
        try:
            team.print_response(question, stream=True)
        except Exception as e:
            logger.error(f"咨询时发生错误: {e}")
            print(f"咨询时发生错误: {e}")
        
        print("\n")

def interactive_team():
    """交互式团队模式"""
    print("=== 交互式团队模式 ===\n")
    print("选择团队类型:")
    print("1. 市场研究团队 (网络搜索 + 金融分析)")
    print("2. 内容创作团队 (研究 + 写作 + 审核)")
    print("3. 专业咨询团队 (技术 + 商业 + 法律)")
    
    choice = input("请选择 (1/2/3): ").strip()
    
    if choice == "1":
        team = create_research_team()
        team_name = "市场研究团队"
    elif choice == "2":
        team = create_content_team()
        team_name = "内容创作团队"
    elif choice == "3":
        team = create_consulting_team()
        team_name = "专业咨询团队"
    else:
        print("无效选择")
        return
    
    if not team:
        print("无法创建团队")
        return
    
    print(f"\n{team_name}已准备就绪！")
    print("你可以提出需要团队协作解决的复杂问题。")
    print("输入 'quit' 或 'exit' 退出。\n")
    
    while True:
        try:
            user_input = input("用户: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出', '结束']:
                print("再见！")
                break
            
            if not user_input:
                continue
                
            print(f"\n{team_name}:")
            team.print_response(user_input, stream=True)
            print("\n" + "="*50 + "\n")
            
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            break
        except Exception as e:
            logger.error(f"发生错误: {e}")
            print(f"发生错误: {e}")

def main():
    """主函数"""
    print("=== Agno 多智能体团队协作示例 ===\n")
    print("选择运行模式:")
    print("1. 市场研究团队演示")
    print("2. 内容创作团队演示")
    print("3. 专业咨询团队演示")
    print("4. 交互式团队模式")
    
    mode = input("请选择模式 (1/2/3/4): ").strip()
    
    if mode == "1":
        demo_research_team()
    elif mode == "2":
        demo_content_team()
    elif mode == "3":
        demo_consulting_team()
    elif mode == "4":
        interactive_team()
    else:
        print("无效选择，默认使用交互式团队模式")
        interactive_team()

if __name__ == "__main__":
    main()