"""
ModelScope 智能体示例

使用 ModelScope 的 OpenAI 兼容 API 创建智能体
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agno.agent import Agent
from agno.models.openai import OpenAIChat
from config import Config
import logging

# 设置日志
logging.basicConfig(level=getattr(logging, Config.LOG_LEVEL))
logger = logging.getLogger(__name__)

def create_modelscope_agent():
    """创建使用 ModelScope API 的智能体"""
    
    if not Config.MODELSCOPE_API_KEY:
        print("错误: ModelScope API 密钥未配置")
        print("请在 .env 文件中设置 MODELSCOPE_API_KEY")
        return None
    
    # 使用 ModelScope 的 OpenAI 兼容接口
    model = OpenAIChat(
        id=Config.MODELSCOPE_MODEL,  # 使用 Qwen3-235B-A22B 模型
        api_key=Config.MODELSCOPE_API_KEY,
        base_url=Config.MODELSCOPE_BASE_URL
    )
    
    agent = Agent(
        model=model,
        name="ModelScope 智能助手",
        description="你是一个基于 ModelScope Qwen3 模型的智能助手，可以帮助用户回答各种问题。",
        instructions=[
            "使用中文回答所有问题",
            "保持友好和专业的语气", 
            "提供准确、详细和有用的信息",
            "如果不确定答案，请诚实地说明",
            "尽可能提供实用的建议和解决方案"
        ],
        markdown=True,
        show_tool_calls=True
    )
    
    return agent

def test_modelscope_connection():
    """测试 ModelScope API 连接"""
    print("=== 测试 ModelScope API 连接 ===\n")
    
    try:
        agent = create_modelscope_agent()
        if not agent:
            return False
        
        print("正在测试 API 连接...")
        response = agent.run("你好，请简单介绍一下自己")
        print(f"✓ 连接成功！")
        print(f"模型响应: {response.content}")
        return True
        
    except Exception as e:
        print(f"✗ 连接失败: {e}")
        return False

def interactive_chat():
    """交互式聊天模式"""
    print("=== ModelScope 智能体交互模式 ===\n")
    
    agent = create_modelscope_agent()
    if not agent:
        return
    
    print("ModelScope 智能助手已启动！")
    print(f"使用模型: {Config.MODELSCOPE_MODEL}")
    print(f"API 端点: {Config.MODELSCOPE_BASE_URL}")
    print("输入 'quit' 或 'exit' 退出。\n")
    
    # 显示一些建议的问题
    print("您可以尝试以下问题：")
    print("- 介绍一下人工智能的发展历史")
    print("- 解释什么是大语言模型")
    print("- 帮我写一个 Python 函数来计算斐波那契数列")
    print("- 推荐一些学习机器学习的资源")
    print()
    
    while True:
        try:
            user_input = input("用户: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出', '结束']:
                print("再见！")
                break
            
            if not user_input:
                continue
                
            print("\nModelScope 助手:")
            agent.print_response(user_input, stream=True)
            print("\n" + "="*50 + "\n")
            
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            break
        except Exception as e:
            logger.error(f"发生错误: {e}")
            print(f"发生错误: {e}")
            print("请检查网络连接和 API 配置\n")

def demo_capabilities():
    """演示智能体的各种能力"""
    print("=== ModelScope 智能体能力演示 ===\n")
    
    agent = create_modelscope_agent()
    if not agent:
        return
    
    # 演示问题列表
    demo_questions = [
        "请解释什么是机器学习，并举一个实际应用的例子",
        "帮我写一个Python函数，用来判断一个数字是否为质数",
        "介绍一下中国的传统节日春节有哪些习俗",
        "如何提高工作效率？给我5个实用的建议",
        "解释一下区块链技术的基本原理"
    ]
    
    print("将演示以下问题的回答：")
    for i, question in enumerate(demo_questions, 1):
        print(f"{i}. {question}")
    
    print("\n开始演示...\n")
    
    for i, question in enumerate(demo_questions, 1):
        print(f"{'='*80}")
        print(f"演示问题 {i}: {question}")
        print(f"{'='*80}")
        
        try:
            agent.print_response(question, stream=True)
        except Exception as e:
            logger.error(f"演示时发生错误: {e}")
            print(f"演示时发生错误: {e}")
        
        # 询问是否继续
        if i < len(demo_questions):
            continue_demo = input("\n按 Enter 继续下一个演示，或输入 'q' 退出: ").strip()
            if continue_demo.lower() == 'q':
                break
        
        print("\n")

def main():
    """主函数"""
    print("=== ModelScope Agno 智能体 ===\n")
    print(f"模型: {Config.MODELSCOPE_MODEL}")
    print(f"API: {Config.MODELSCOPE_BASE_URL}")
    print()
    
    # 首先测试连接
    if not test_modelscope_connection():
        print("\n请检查 ModelScope API 配置后重试")
        return
    
    print("\n选择运行模式:")
    print("1. 交互式聊天")
    print("2. 能力演示") 
    print("3. 仅测试连接")
    
    try:
        choice = input("\n请选择 (1/2/3): ").strip()
        
        if choice == "1":
            interactive_chat()
        elif choice == "2":
            demo_capabilities()
        elif choice == "3":
            print("连接测试已完成")
        else:
            print("无效选择，启动交互模式")
            interactive_chat()
    
    except KeyboardInterrupt:
        print("\n程序被用户中断")

if __name__ == "__main__":
    main()