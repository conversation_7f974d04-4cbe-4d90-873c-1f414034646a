"""
推理智能体示例

这个示例展示如何创建具备推理能力的智能体，能够进行逐步分析和思考。
"""

from agno.agent import Agent
from agno.models.anthropic import Claude
from agno.models.openai import OpenAIChat
from agno.tools.reasoning import ReasoningTools
from agno.tools.yfinance import YFinanceTools
from config import Config
import logging

# 设置日志
logging.basicConfig(level=getattr(logging, Config.LOG_LEVEL))
logger = logging.getLogger(__name__)

def create_reasoning_analyst():
    """创建一个具备推理能力的分析师智能体"""
    
    if not Config.validate_api_keys():
        return None
    
    # 优先使用 Claude，因为它在推理方面表现更好
    if Config.ANTHROPIC_API_KEY:
        model = Claude(id=Config.DEFAULT_CLAUDE_MODEL)
    else:
        model = OpenAIChat(id=Config.DEFAULT_OPENAI_MODEL)
    
    agent = Agent(
        model=model,
        name="推理分析师",
        description="你是一个专业的分析师，具备强大的推理和分析能力。",
        instructions=[
            "使用中文进行分析和回答",
            "进行逐步推理，展示思考过程",
            "使用表格展示数据",
            "只输出分析报告，不包含其他文本",
            "确保分析的逻辑性和准确性",
            "包含风险评估和建议"
        ],
        tools=[
            ReasoningTools(
                add_instructions=True,  # 添加推理指令
                add_few_shot=True      # 添加少样本示例
            ),
            YFinanceTools(
                stock_price=True,
                analyst_recommendations=True,
                company_info=True,
                company_news=True
            )
        ],
        show_tool_calls=True,
        markdown=True
    )
    
    return agent

def create_problem_solver():
    """创建一个问题解决专家智能体"""
    
    if not Config.validate_api_keys():
        return None
    
    if Config.ANTHROPIC_API_KEY:
        model = Claude(id=Config.DEFAULT_CLAUDE_MODEL)
    else:
        model = OpenAIChat(id=Config.DEFAULT_OPENAI_MODEL)
    
    agent = Agent(
        model=model,
        name="问题解决专家",
        description="你是一个专业的问题解决专家，擅长分析复杂问题并提供解决方案。",
        instructions=[
            "使用中文回答",
            "对问题进行系统性分析",
            "提供多种解决方案",
            "评估每种方案的优缺点",
            "给出最佳建议",
            "展示详细的推理过程"
        ],
        tools=[
            ReasoningTools(
                add_instructions=True,
                add_few_shot=True
            )
        ],
        show_tool_calls=True,
        markdown=True
    )
    
    return agent

def demo_stock_analysis():
    """演示股票分析"""
    print("=== 股票分析演示 ===\n")
    
    agent = create_reasoning_analyst()
    if not agent:
        print("无法创建分析师智能体")
        return
    
    # 股票分析任务
    analysis_task = "分析英伟达(NVDA)的投资价值，包括财务表现、市场地位、增长前景和投资风险"
    
    print(f"分析任务: {analysis_task}")
    print("="*80)
    
    try:
        agent.print_response(
            analysis_task,
            stream=True,
            show_full_reasoning=True,          # 显示完整推理过程
            stream_intermediate_steps=True     # 流式显示中间步骤
        )
    except Exception as e:
        logger.error(f"股票分析时发生错误: {e}")
        print(f"分析时发生错误: {e}")

def demo_problem_solving():
    """演示问题解决"""
    print("=== 问题解决演示 ===\n")
    
    agent = create_problem_solver()
    if not agent:
        print("无法创建问题解决专家")
        return
    
    # 复杂问题示例
    problems = [
        "如何提高一个初创科技公司的用户留存率？",
        "分析远程工作对团队协作的影响，并提出改进方案",
        "设计一个可持续的城市交通解决方案"
    ]
    
    print("将演示以下问题的解决过程:")
    for i, problem in enumerate(problems, 1):
        print(f"{i}. {problem}")
    
    print("\n开始问题解决演示...\n")
    
    for i, problem in enumerate(problems, 1):
        print(f"{'='*80}")
        print(f"问题 {i}: {problem}")
        print(f"{'='*80}")
        
        try:
            agent.print_response(
                problem,
                stream=True,
                show_full_reasoning=True,
                stream_intermediate_steps=True
            )
        except Exception as e:
            logger.error(f"解决问题时发生错误: {e}")
            print(f"解决问题时发生错误: {e}")
        
        print("\n")

def interactive_reasoning():
    """交互式推理模式"""
    print("=== 交互式推理模式 ===\n")
    print("选择智能体类型:")
    print("1. 推理分析师 (擅长数据分析和投资建议)")
    print("2. 问题解决专家 (擅长复杂问题分析)")
    
    choice = input("请选择 (1/2): ").strip()
    
    if choice == "1":
        agent = create_reasoning_analyst()
        agent_name = "推理分析师"
    elif choice == "2":
        agent = create_problem_solver()
        agent_name = "问题解决专家"
    else:
        print("无效选择")
        return
    
    if not agent:
        print("无法创建智能体")
        return
    
    print(f"\n{agent_name}已准备就绪！")
    print("你可以提出需要深度分析和推理的问题。")
    print("输入 'quit' 或 'exit' 退出。\n")
    
    while True:
        try:
            user_input = input("用户: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出', '结束']:
                print("再见！")
                break
            
            if not user_input:
                continue
                
            print(f"\n{agent_name}:")
            agent.print_response(
                user_input,
                stream=True,
                show_full_reasoning=True,
                stream_intermediate_steps=True
            )
            print("\n" + "="*50 + "\n")
            
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            break
        except Exception as e:
            logger.error(f"发生错误: {e}")
            print(f"发生错误: {e}")

def main():
    """主函数"""
    print("=== Agno 推理智能体示例 ===\n")
    print("选择运行模式:")
    print("1. 股票分析演示")
    print("2. 问题解决演示")
    print("3. 交互式推理模式")
    
    mode = input("请选择模式 (1/2/3): ").strip()
    
    if mode == "1":
        demo_stock_analysis()
    elif mode == "2":
        demo_problem_solving()
    elif mode == "3":
        interactive_reasoning()
    else:
        print("无效选择，默认使用交互式推理模式")
        interactive_reasoning()

if __name__ == "__main__":
    main()