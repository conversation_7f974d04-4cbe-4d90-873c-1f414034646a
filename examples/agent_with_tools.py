"""
带工具的智能体示例

这个示例展示如何创建一个具备搜索和数据获取工具的智能体。
"""

from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.tools.duckduckgo import DuckDuckGoTools
from agno.tools.yfinance import YFinanceTools
from config import Config
import logging

# 设置日志
logging.basicConfig(level=getattr(logging, Config.LOG_LEVEL))
logger = logging.getLogger(__name__)

def create_research_agent():
    """创建一个具备研究能力的智能体"""
    
    if not Config.validate_api_keys():
        return None
    
    agent = Agent(
        model=OpenAIChat(id=Config.DEFAULT_OPENAI_MODEL),
        name="研究助手",
        description="你是一个专业的研究助手，能够搜索网络信息和获取金融数据。",
        instructions=[
            "使用中文回答所有问题",
            "当需要最新信息时，使用搜索工具",
            "当涉及股票或金融数据时，使用金融工具",
            "始终包含信息来源",
            "提供准确、详细的信息",
            "使用表格展示数据（当适用时）"
        ],
        tools=[
            DuckDuckGoTools(),  # 网络搜索工具
            YFinanceTools(      # 金融数据工具
                stock_price=True,
                analyst_recommendations=True,
                company_info=True,
                company_news=True
            )
        ],
        show_tool_calls=True,
        markdown=True
    )
    
    return agent

def create_news_agent():
    """创建一个新闻智能体"""
    
    if not Config.validate_api_keys():
        return None
    
    agent = Agent(
        model=OpenAIChat(id=Config.DEFAULT_OPENAI_MODEL),
        name="新闻记者",
        description="你是一个充满热情的新闻记者，擅长讲故事！",
        instructions=[
            "使用中文撰写新闻",
            "保持新闻的准确性和时效性",
            "用生动有趣的语言",
            "始终包含信息来源",
            "按照新闻格式组织内容"
        ],
        tools=[DuckDuckGoTools()],
        show_tool_calls=True,
        markdown=True
    )
    
    return agent

def demo_research_agent():
    """演示研究助手的使用"""
    print("=== 研究助手演示 ===\n")
    
    agent = create_research_agent()
    if not agent:
        print("无法创建研究助手")
        return
    
    # 预设的研究问题
    research_questions = [
        "搜索苹果公司最新的财务新闻",
        "获取特斯拉(TSLA)的股价信息和分析师建议",
        "搜索人工智能技术的最新发展趋势"
    ]
    
    print("研究助手已启动，将演示以下研究任务：")
    for i, question in enumerate(research_questions, 1):
        print(f"{i}. {question}")
    
    print("\n开始执行研究任务...\n")
    
    for i, question in enumerate(research_questions, 1):
        print(f"{'='*60}")
        print(f"研究任务 {i}: {question}")
        print(f"{'='*60}")
        
        try:
            agent.print_response(question, stream=True)
        except Exception as e:
            logger.error(f"执行研究任务时发生错误: {e}")
            print(f"执行任务时发生错误: {e}")
        
        print("\n")

def interactive_mode():
    """交互模式"""
    print("=== 交互模式 ===\n")
    print("选择智能体类型:")
    print("1. 研究助手 (带搜索和金融工具)")
    print("2. 新闻记者 (带搜索工具)")
    
    choice = input("请选择 (1/2): ").strip()
    
    if choice == "1":
        agent = create_research_agent()
        agent_name = "研究助手"
    elif choice == "2":
        agent = create_news_agent()
        agent_name = "新闻记者"
    else:
        print("无效选择")
        return
    
    if not agent:
        print("无法创建智能体")
        return
    
    print(f"\n{agent_name}已准备就绪！")
    print("你可以询问任何需要搜索或数据获取的问题。")
    print("输入 'quit' 或 'exit' 退出。\n")
    
    while True:
        try:
            user_input = input("用户: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出', '结束']:
                print("再见！")
                break
            
            if not user_input:
                continue
                
            print(f"\n{agent_name}:")
            agent.print_response(user_input, stream=True)
            print("\n" + "="*50 + "\n")
            
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            break
        except Exception as e:
            logger.error(f"发生错误: {e}")
            print(f"发生错误: {e}")

def main():
    """主函数"""
    print("=== Agno 带工具的智能体示例 ===\n")
    print("选择运行模式:")
    print("1. 演示模式 (自动执行预设任务)")
    print("2. 交互模式 (手动输入问题)")
    
    mode = input("请选择模式 (1/2): ").strip()
    
    if mode == "1":
        demo_research_agent()
    elif mode == "2":
        interactive_mode()
    else:
        print("无效选择，默认使用交互模式")
        interactive_mode()

if __name__ == "__main__":
    main()