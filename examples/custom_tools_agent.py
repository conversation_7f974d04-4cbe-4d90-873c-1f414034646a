"""
自定义工具智能体示例

这个示例展示如何创建使用自定义工具的智能体。
"""

from agno.agent import Agent
from agno.models.openai import OpenAIChat
from tools.custom_tools import get_custom_tools
from config import Config
import logging

# 设置日志
logging.basicConfig(level=getattr(logging, Config.LOG_LEVEL))
logger = logging.getLogger(__name__)

def create_utility_agent():
    """创建一个实用工具智能体"""
    
    if not Config.validate_api_keys():
        return None
    
    # 获取所有自定义工具
    custom_tools = get_custom_tools()
    
    agent = Agent(
        model=OpenAIChat(id=Config.DEFAULT_OPENAI_MODEL),
        name="实用工具助手",
        description="你是一个实用工具助手，可以帮助用户进行各种实用计算和查询。",
        instructions=[
            "使用中文回答所有问题",
            "充分利用可用的工具来帮助用户",
            "对于天气查询，使用天气工具",
            "对于数学计算，使用计算器工具",
            "对于文本处理，使用文本处理工具",
            "对于时间相关问题，使用时间工具",
            "提供准确、有用的信息"
        ],
        tools=custom_tools,
        show_tool_calls=True,
        markdown=True
    )
    
    return agent

def demo_utility_functions():
    """演示实用工具功能"""
    print("=== 实用工具智能体演示 ===\n")
    
    agent = create_utility_agent()
    if not agent:
        print("无法创建实用工具智能体")
        return
    
    # 演示任务
    demo_tasks = [
        "查询北京的天气情况",
        "计算 25 * 4 + 100 / 5",
        "将 100 米转换为英尺",
        "分析这段文本的统计信息：'人工智能技术正在快速发展，它将改变我们的生活方式。'",
        "获取当前时间",
        "计算 2024-01-01 和 2024-12-31 之间相差多少天"
    ]
    
    print("将演示以下实用工具功能:")
    for i, task in enumerate(demo_tasks, 1):
        print(f"{i}. {task}")
    
    print("\n开始演示...\n")
    
    for i, task in enumerate(demo_tasks, 1):
        print(f"{'='*60}")
        print(f"任务 {i}: {task}")
        print(f"{'='*60}")
        
        try:
            agent.print_response(task, stream=True)
        except Exception as e:
            logger.error(f"执行任务时发生错误: {e}")
            print(f"执行任务时发生错误: {e}")
        
        print("\n")

def interactive_utility():
    """交互式实用工具模式"""
    print("=== 交互式实用工具模式 ===\n")
    
    agent = create_utility_agent()
    if not agent:
        print("无法创建实用工具智能体")
        return
    
    print("实用工具助手已准备就绪！")
    print("可用功能：")
    print("- 天气查询（支持：北京、上海、广州、深圳）")
    print("- 数学计算和单位转换")
    print("- 文本分析和格式化")
    print("- 时间查询和计算")
    print("\n输入 'quit' 或 'exit' 退出。\n")
    
    # 显示一些使用示例
    print("使用示例：")
    print("- '查询上海天气'")
    print("- '计算 123 * 456'")
    print("- '将 5 公里转换为英里'")
    print("- '分析这段文本的字数：你好世界'")
    print("- '现在几点了'")
    print("- '计算 2024-01-01 到今天相差多少天'\n")
    
    while True:
        try:
            user_input = input("用户: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出', '结束']:
                print("再见！")
                break
            
            if not user_input:
                continue
                
            print("\n实用工具助手:")
            agent.print_response(user_input, stream=True)
            print("\n" + "="*50 + "\n")
            
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            break
        except Exception as e:
            logger.error(f"发生错误: {e}")
            print(f"发生错误: {e}")

def main():
    """主函数"""
    print("=== Agno 自定义工具智能体示例 ===\n")
    print("选择运行模式:")
    print("1. 演示模式 (展示各种工具功能)")
    print("2. 交互模式 (手动使用工具)")
    
    mode = input("请选择模式 (1/2): ").strip()
    
    if mode == "1":
        demo_utility_functions()
    elif mode == "2":
        interactive_utility()
    else:
        print("无效选择，默认使用交互模式")
        interactive_utility()

if __name__ == "__main__":
    main()