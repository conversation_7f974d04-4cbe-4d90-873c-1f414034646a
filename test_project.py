#!/usr/bin/env python3
"""
Agno 智能体项目测试脚本

此脚本用于测试项目的各个组件是否正常工作。
"""

import sys
import os
from pathlib import Path
from config import Config

def test_imports():
    """测试关键模块导入"""
    print("测试模块导入...")
    
    try:
        import agno
        print("✓ agno 模块导入成功")
    except ImportError as e:
        print(f"✗ agno 模块导入失败: {e}")
        return False
    
    try:
        from agno.agent import Agent
        from agno.models.openai import OpenAIChat
        print("✓ agno 核心组件导入成功")
    except ImportError as e:
        print(f"✗ agno 核心组件导入失败: {e}")
        return False
    
    try:
        from tools.custom_tools import get_custom_tools
        print("✓ 自定义工具导入成功")
    except ImportError as e:
        print(f"✗ 自定义工具导入失败: {e}")
        return False
    
    return True

def test_configuration():
    """测试配置"""
    print("\n测试配置...")
    
    # 测试环境变量文件
    if Path('.env').exists():
        print("✓ .env 文件存在")
    else:
        print("✗ .env 文件不存在")
        return False
    
    # 测试 API 密钥
    if Config.OPENAI_API_KEY:
        print("✓ OpenAI API 密钥已配置")
    else:
        print("✗ OpenAI API 密钥未配置")
    
    if Config.ANTHROPIC_API_KEY:
        print("✓ Anthropic API 密钥已配置")
    else:
        print("⚠ Anthropic API 密钥未配置")
    
    return True

def test_directories():
    """测试目录结构"""
    print("\n测试目录结构...")
    
    required_dirs = ['examples', 'tools', 'data', 'logs']
    all_exist = True
    
    for dir_name in required_dirs:
        if Path(dir_name).exists():
            print(f"✓ 目录 {dir_name} 存在")
        else:
            print(f"✗ 目录 {dir_name} 不存在")
            all_exist = False
    
    return all_exist

def test_example_files():
    """测试示例文件"""
    print("\n测试示例文件...")
    
    example_files = [
        'basic_agent.py',
        'agent_with_tools.py', 
        'reasoning_agent.py',
        'multi_agent_team.py',
        'custom_tools_agent.py'
    ]
    
    all_exist = True
    
    for file_name in example_files:
        file_path = Path('examples') / file_name
        if file_path.exists():
            print(f"✓ 示例文件 {file_name} 存在")
        else:
            print(f"✗ 示例文件 {file_name} 不存在")
            all_exist = False
    
    return all_exist

def test_basic_agent():
    """测试基础智能体创建"""
    print("\n测试基础智能体创建...")
    
    if not Config.OPENAI_API_KEY:
        print("⚠ 跳过智能体测试（未配置 OpenAI API 密钥）")
        return True
    
    try:
        from agno.agent import Agent
        from agno.models.openai import OpenAIChat
        
        agent = Agent(
            model=OpenAIChat(id="gpt-3.5-turbo"),
            description="测试智能体",
            instructions=["简短回答"]
        )
        
        print("✓ 基础智能体创建成功")
        return True
        
    except Exception as e:
        print(f"✗ 基础智能体创建失败: {e}")
        return False

def test_custom_tools():
    """测试自定义工具"""
    print("\n测试自定义工具...")
    
    try:
        from tools.custom_tools import (
            WeatherTool, 
            CalculatorTool, 
            TextProcessingTool, 
            TimeUtilTool
        )
        
        # 测试天气工具
        weather_tool = WeatherTool()
        result = weather_tool.get_weather("北京")
        print(f"✓ 天气工具测试: {result[:50]}...")
        
        # 测试计算器工具
        calc_tool = CalculatorTool()
        result = calc_tool.calculate("2+3")
        print(f"✓ 计算器工具测试: {result}")
        
        # 测试文本处理工具
        text_tool = TextProcessingTool()
        result = text_tool.analyze_text("测试文本")
        print("✓ 文本处理工具测试成功")
        
        # 测试时间工具
        time_tool = TimeUtilTool()
        result = time_tool.get_current_time()
        print(f"✓ 时间工具测试: {result}")
        
        return True
        
    except Exception as e:
        print(f"✗ 自定义工具测试失败: {e}")
        return False

def test_dependencies():
    """测试依赖库"""
    print("\n测试依赖库...")
    
    dependencies = [
        'agno',
        'openai',
        'anthropic',
        'yfinance',
        'duckduckgo_search',
        'lancedb',
        'pandas',
        'numpy',
        'python_dotenv',
        'requests'
    ]
    
    missing_deps = []
    
    for dep in dependencies:
        try:
            if dep == 'duckduckgo_search':
                import duckduckgo_search
            elif dep == 'python_dotenv':
                import dotenv
            else:
                __import__(dep)
            print(f"✓ {dep} 已安装")
        except ImportError:
            print(f"✗ {dep} 未安装")
            missing_deps.append(dep)
    
    if missing_deps:
        print(f"\n缺少依赖: {', '.join(missing_deps)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    return True

def run_comprehensive_test():
    """运行综合测试"""
    print("=" * 60)
    print("           Agno 智能体项目综合测试")
    print("=" * 60)
    
    tests = [
        ("模块导入", test_imports),
        ("项目配置", test_configuration),
        ("目录结构", test_directories),
        ("示例文件", test_example_files),
        ("依赖库", test_dependencies),
        ("自定义工具", test_custom_tools),
        ("基础智能体", test_basic_agent)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'=' * 40}")
        print(f"测试: {test_name}")
        print(f"{'=' * 40}")
        
        try:
            if test_func():
                passed_tests += 1
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试都通过了！项目配置正确。")
        return True
    else:
        print("⚠ 部分测试失败，请检查配置。")
        return False

def quick_test():
    """快速测试"""
    print("运行快速测试...")
    
    # 基本导入测试
    if not test_imports():
        return False
    
    # 配置测试
    if not test_configuration():
        return False
    
    print("\n✓ 快速测试通过")
    return True

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == "--quick":
        quick_test()
    else:
        run_comprehensive_test()

if __name__ == "__main__":
    main()