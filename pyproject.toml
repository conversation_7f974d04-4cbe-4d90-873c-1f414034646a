[project]
name = "agno-agent"
version = "1.0.0"
description = "基于 Agno 框架构建的智能体项目"
authors = [
    {name = "User", email = "<EMAIL>"},
]
dependencies = [
    "agno>=1.8.0",
    "openai>=1.0.0",
    "anthropic>=0.60.0",
    "yfinance>=0.2.0",
    "duckduckgo-search>=8.0.0",
    "lancedb>=0.20.0",
    "pandas>=2.0.0",
    "numpy>=1.24.0",
    "python-dotenv>=1.0.0",
    "requests>=2.28.0",
    "beautifulsoup4>=4.11.0",
    "matplotlib>=3.6.0",
    "plotly>=5.0.0",
]
requires-python = ">=3.8"
readme = "README.md"
license = {text = "MIT"}

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
]

[tool.hatch.build.targets.wheel]
packages = ["examples", "tools"]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"