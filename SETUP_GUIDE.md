# Agno 智能体安装和使用指南

本指南将详细介绍如何安装和使用基于 Agno 框架的智能体项目。

## 安装指南

### 系统要求

- Python 3.8 或更高版本
- pip 包管理器
- 稳定的网络连接（用于 API 调用）

### 步骤 1: 环境准备

#### 使用虚拟环境（推荐）

```bash
# 创建虚拟环境
python -m venv agno_env

# 激活虚拟环境
# Windows:
agno_env\Scripts\activate
# macOS/Linux:
source agno_env/bin/activate
```

#### 使用 conda（可选）

```bash
# 创建 conda 环境
conda create -n agno_env python=3.10
conda activate agno_env
```

### 步骤 2: 安装依赖

```bash
# 进入项目目录
cd agno_agent

# 安装项目依赖
pip install -r requirements.txt

# 验证安装
python -c "import agno; print('Agno 安装成功！')"
```

### 步骤 3: 配置环境变量

1. 复制环境变量模板：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，设置您的 API 密钥：

```bash
# 使用文本编辑器打开 .env 文件
nano .env  # 或使用其他编辑器
```

3. 在 `.env` 文件中设置以下信息：

```bash
# OpenAI API Key（必需）
OPENAI_API_KEY=sk-your-openai-api-key-here

# Anthropic API Key（可选，用于 Claude 模型）
ANTHROPIC_API_KEY=sk-ant-api03-your-anthropic-key-here

# 其他配置（可选）
AGNO_TELEMETRY=true
LOG_LEVEL=INFO
VECTOR_DB_PATH=./data/vectordb
```

## API 密钥获取

### OpenAI API 密钥

1. 访问 [OpenAI Platform](https://platform.openai.com/)
2. 创建账户或登录
3. 进入 "API Keys" 页面
4. 点击 "Create new secret key"
5. 复制生成的密钥到 `.env` 文件

### Anthropic API 密钥

1. 访问 [Anthropic Console](https://console.anthropic.com/)
2. 创建账户或登录
3. 进入 "API Keys" 页面
4. 生成新的 API 密钥
5. 复制密钥到 `.env` 文件

## 使用指南

### 快速测试

运行基础智能体测试连接：

```bash
python examples/basic_agent.py
```

如果配置正确，您将看到智能体启动并可以开始对话。

### 示例程序说明

#### 1. 基础智能体 (`basic_agent.py`)

**功能**: 基本的对话智能体

**运行方式**:
```bash
python examples/basic_agent.py
```

**特点**:
- 简单的问答功能
- 支持中文对话
- 展示 Agno 的基本用法

#### 2. 工具智能体 (`agent_with_tools.py`)

**功能**: 集成搜索和金融数据工具

**运行方式**:
```bash
python examples/agent_with_tools.py
```

**可用功能**:
- 网络搜索（DuckDuckGo）
- 股票数据查询（Yahoo Finance）
- 新闻信息获取

**使用示例**:
- "搜索苹果公司最新新闻"
- "查询特斯拉股价"
- "告诉我关于人工智能的最新发展"

#### 3. 推理智能体 (`reasoning_agent.py`)

**功能**: 具备逐步推理能力的智能体

**运行方式**:
```bash
python examples/reasoning_agent.py
```

**特点**:
- 显示完整推理过程
- 逐步分析问题
- 适合复杂决策任务

**使用示例**:
- "分析投资 NVDA 股票的利弊"
- "如何解决团队沟通问题"
- "制定市场营销策略"

#### 4. 多智能体团队 (`multi_agent_team.py`)

**功能**: 多个智能体协作完成任务

**运行方式**:
```bash
python examples/multi_agent_team.py
```

**团队类型**:
- **研究团队**: 网络搜索 + 金融分析
- **内容团队**: 研究 + 写作 + 审核
- **咨询团队**: 技术 + 商业 + 法律专家

**使用示例**:
- "分析AI芯片市场前景"
- "写一篇关于可持续能源的文章"
- "开发AI应用需要注意什么"

#### 5. 自定义工具智能体 (`custom_tools_agent.py`)

**功能**: 使用自定义工具的智能体

**运行方式**:
```bash
python examples/custom_tools_agent.py
```

**可用工具**:
- 天气查询
- 数学计算器
- 文本分析
- 时间工具

**使用示例**:
- "查询北京天气"
- "计算 123 * 456"
- "将 100 米转换为英尺"
- "现在几点了"

## 高级配置

### 自定义模型配置

在 `config.py` 中修改默认模型：

```python
# 修改默认 OpenAI 模型
DEFAULT_OPENAI_MODEL = "gpt-4o-mini"  # 更便宜的选项

# 修改默认 Claude 模型
DEFAULT_CLAUDE_MODEL = "claude-3-haiku-20240307"  # 更快的选项
```

### 日志配置

设置不同的日志级别：

```bash
# 在 .env 文件中设置
LOG_LEVEL=DEBUG    # 详细调试信息
LOG_LEVEL=INFO     # 一般信息（默认）
LOG_LEVEL=WARNING  # 仅警告和错误
LOG_LEVEL=ERROR    # 仅错误信息
```

### 性能调优

#### 并发处理

Agno 支持高并发处理，可以同时运行多个智能体实例：

```python
import asyncio
from agno.agent import Agent

async def run_multiple_agents():
    agents = [create_agent() for _ in range(10)]
    tasks = [agent.arun_response("Hello") for agent in agents]
    results = await asyncio.gather(*tasks)
    return results
```

#### 内存管理

对于长时间运行的应用，建议定期清理会话历史：

```python
# 清理智能体的会话历史
agent.clear_memory()
```

## 故障排除

### 常见错误及解决方案

#### 1. 导入错误

**错误信息**: `ModuleNotFoundError: No module named 'agno'`

**解决方案**:
```bash
# 确保在正确的虚拟环境中
pip install agno

# 或重新安装所有依赖
pip install -r requirements.txt
```

#### 2. API 密钥错误

**错误信息**: `Invalid API key` 或 `Authentication failed`

**解决方案**:
1. 检查 `.env` 文件是否存在
2. 确认 API 密钥格式正确
3. 验证 API 密钥是否有效
4. 检查账户余额

#### 3. 网络连接问题

**错误信息**: `Connection timeout` 或 `Network error`

**解决方案**:
1. 检查网络连接
2. 验证防火墙设置
3. 尝试使用代理（如需要）

#### 4. 权限错误

**错误信息**: `Permission denied` 或无法创建文件

**解决方案**:
```bash
# 确保有足够的文件权限
chmod +x examples/*.py

# 检查目录权限
ls -la data/ logs/
```

### 调试技巧

#### 启用详细日志

```bash
# 设置调试级别日志
export LOG_LEVEL=DEBUG
python examples/basic_agent.py
```

#### 查看工具调用过程

在创建智能体时设置：

```python
agent = Agent(
    # ... 其他配置
    show_tool_calls=True,  # 显示工具调用
    debug=True             # 启用调试模式
)
```

#### 测试 API 连接

```python
# 测试 OpenAI 连接
from agno.models.openai import OpenAIChat
model = OpenAIChat(id="gpt-3.5-turbo")
response = model.run("Hello")
print(response)

# 测试 Anthropic 连接
from agno.models.anthropic import Claude
model = Claude(id="claude-3-haiku-20240307")
response = model.run("Hello")
print(response)
```

## 最佳实践

### 1. 成本控制

- 使用较小的模型进行测试（如 gpt-3.5-turbo）
- 设置合理的 token 限制
- 监控 API 使用情况

### 2. 性能优化

- 合理使用缓存
- 避免频繁的模型切换
- 使用批处理处理大量请求

### 3. 安全考虑

- 不要在代码中硬编码 API 密钥
- 定期轮换 API 密钥
- 限制智能体的权限范围

### 4. 错误处理

- 实现重试机制
- 处理网络异常
- 记录详细的错误日志

## 下一步

1. 尝试运行所有示例程序
2. 根据需求修改智能体配置
3. 开发自定义工具
4. 创建专门的智能体应用
5. 探索多智能体协作模式

如需更多帮助，请参考 [README.md](README.md) 或提交 Issue。