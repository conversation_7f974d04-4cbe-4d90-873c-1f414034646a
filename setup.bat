@echo off
REM Agno 智能体项目初始化脚本 (Windows)
REM 此脚本用于快速设置和初始化项目环境

echo ======================================
echo     Agno 智能体项目初始化脚本
echo ======================================

REM 检查 Python 版本
echo 检查 Python 版本...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ✗ Python 未安装或未添加到 PATH
    echo 请访问 https://python.org 下载并安装 Python 3.8+
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set python_version=%%i
echo ✓ Python 版本: %python_version%

REM 检查是否在虚拟环境中
if defined VIRTUAL_ENV (
    echo ✓ 正在虚拟环境中: %VIRTUAL_ENV%
) else (
    echo ⚠ 未检测到虚拟环境
    set /p create_venv="是否创建虚拟环境？(y/n): "
    
    if /i "%create_venv%"=="y" (
        echo 创建虚拟环境...
        python -m venv agno_env
        echo 激活虚拟环境...
        call agno_env\Scripts\activate.bat
        echo ✓ 虚拟环境已创建并激活
    )
)

REM 安装依赖
echo 安装项目依赖...
python -m pip install --upgrade pip
python -m pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo ✗ 依赖安装失败
    pause
    exit /b 1
)

echo ✓ 依赖安装完成

REM 创建环境变量文件
if not exist .env (
    echo 创建环境变量文件...
    copy .env.example .env >nul
    echo ✓ .env 文件已创建
    echo ⚠ 请编辑 .env 文件并设置您的 API 密钥
) else (
    echo ✓ .env 文件已存在
)

REM 创建必要目录
echo 创建项目目录...
if not exist data mkdir data
if not exist logs mkdir logs

echo ✓ 项目目录创建完成

REM 验证安装
echo 验证安装...
python -c "import agno; print('Agno 安装成功')" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Agno 框架安装成功
) else (
    echo ✗ Agno 框架安装失败
    pause
    exit /b 1
)

echo.
echo ======================================
echo         初始化完成！
echo ======================================
echo.
echo 下一步操作：
echo 1. 编辑 .env 文件，设置您的 API 密钥
echo 2. 运行: python main.py
echo 3. 选择要体验的智能体示例
echo.
echo API 密钥获取：
echo - OpenAI: https://platform.openai.com/api-keys
echo - Anthropic: https://console.anthropic.com/account/keys
echo.

REM 询问是否立即运行
set /p run_main="是否立即运行主程序？(y/n): "

if /i "%run_main%"=="y" (
    echo 启动 Agno 智能体项目...
    python main.py
)

pause