#!/usr/bin/env python3
"""
Agno 官方 Playground 简化启动脚本

启动 Agno 框架的官方 Playground Web 界面
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agno.agent import Agent
from agno.models.openai import OpenAIChat
from config import Config
import logging

# 设置日志
logging.basicConfig(level=getattr(logging, Config.LOG_LEVEL))
logger = logging.getLogger(__name__)

def create_simple_agent():
    """创建一个简单的 ModelScope 智能体"""
    
    if not Config.MODELSCOPE_API_KEY:
        print("错误: ModelScope API 密钥未配置")
        return None
    
    try:
        # 创建模型
        model = OpenAIChat(
            id=Config.MODELSCOPE_MODEL,
            api_key=Config.MODELSCOPE_API_KEY,
            base_url=Config.MODELSCOPE_BASE_URL
        )
        
        # 创建智能体
        agent = Agent(
            model=model,
            name="ModelScope 智能助手",
            description="基于 ModelScope Qwen3-235B-A22B 的智能助手",
            instructions=[
                "使用中文回答所有问题",
                "保持友好和专业的语气",
                "提供准确和有用的信息"
            ],
            markdown=True,
            show_tool_calls=True,
            debug_mode=True  # 启用调试模式
        )
        
        return agent
        
    except Exception as e:
        print(f"创建智能体失败: {e}")
        return None

def test_agent():
    """测试智能体"""
    
    print("=== 测试 ModelScope 智能体 ===\n")
    
    agent = create_simple_agent()
    if not agent:
        return False
    
    try:
        print("正在测试智能体...")
        response = agent.run("你好，请简单介绍一下自己")
        print(f"✓ 智能体测试成功！")
        print(f"响应: {response.content}")
        return True
        
    except Exception as e:
        print(f"✗ 智能体测试失败: {e}")
        return False

def start_playground_server():
    """启动 Playground 服务器"""
    
    print("=== 启动 Agno Playground 服务 ===\n")
    
    # 先测试智能体
    if not test_agent():
        print("智能体测试失败，无法启动 Playground")
        return
    
    agent = create_simple_agent()
    
    try:
        # 方法1: 使用 agent.serve() 
        print("正在启动 Playground Web 界面...")
        print("访问地址: http://localhost:7777")
        print("按 Ctrl+C 停止服务")
        print()
        
        # 启动服务
        agent.serve(
            host="localhost",
            port=7777,
            reload=True,
            debug=True
        )
        
    except AttributeError:
        print("当前 Agno 版本不支持 serve() 方法")
        try_playground_class(agent)
        
    except Exception as e:
        print(f"启动服务失败: {e}")
        try_playground_class(agent)

def try_playground_class(agent):
    """尝试使用 Playground 类"""
    
    print("\\n=== 尝试使用 Playground 类 ===")
    
    try:
        from agno.playground import Playground
        
        playground = Playground(
            agents=[agent],
            name="ModelScope Playground"
        )
        
        print("Playground 创建成功，正在启动服务...")
        playground.serve(host="localhost", port=7777)
        
    except ImportError:
        print("无法导入 Playground 类")
        try_alternative_methods(agent)
    except Exception as e:
        print(f"Playground 启动失败: {e}")
        try_alternative_methods(agent)

def try_alternative_methods(agent):
    """尝试其他启动方法"""
    
    print("\\n=== 尝试其他启动方法 ===")
    
    # 方法1: 检查是否有 app 属性
    try:
        if hasattr(agent, 'app'):
            print("找到 agent.app，尝试启动...")
            import uvicorn
            uvicorn.run(agent.app, host="localhost", port=7777)
            return
    except Exception as e:
        print(f"方法1失败: {e}")
    
    # 方法2: 尝试直接创建 FastAPI 应用
    try:
        print("尝试创建自定义 Web 界面...")
        create_custom_web_interface(agent)
    except Exception as e:
        print(f"方法2失败: {e}")
    
    # 最后的建议
    print("\\n建议:")
    print("1. 检查 Agno 版本: pip show agno")
    print("2. 升级到最新版本: pip install -U agno")
    print("3. 查看官方文档: https://docs.agno.com")
    print("4. 使用命令行测试: python -c \\\"from agno.agent import Agent; print('Agno 可用')\\\"")

def create_custom_web_interface(agent):
    """创建自定义 Web 界面"""
    
    try:
        from fastapi import FastAPI, Request
        from fastapi.responses import HTMLResponse
        import uvicorn
        
        app = FastAPI(title="ModelScope Playground")
        
        @app.get("/", response_class=HTMLResponse)
        async def home():
            return f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>ModelScope Playground</title>
                <meta charset="utf-8">
            </head>
            <body>
                <h1>ModelScope Playground</h1>
                <p>智能体: {agent.name}</p>
                <p>模型: {Config.MODELSCOPE_MODEL}</p>
                <p>API: {Config.MODELSCOPE_BASE_URL}</p>
                
                <h2>测试对话</h2>
                <form action="/chat" method="post">
                    <textarea name="message" placeholder="输入您的问题..." rows="3" cols="50"></textarea><br>
                    <button type="submit">发送</button>
                </form>
                
                <h2>说明</h2>
                <p>这是一个简化的 Web 界面。完整的 Playground 功能需要升级 Agno 版本。</p>
            </body>
            </html>
            """
        
        @app.post("/chat")
        async def chat(request: Request):
            form = await request.form()
            message = form.get("message", "")
            
            if message:
                try:
                    response = agent.run(message)
                    return {"response": response.content}
                except Exception as e:
                    return {"error": str(e)}
            
            return {"error": "没有收到消息"}
        
        print("自定义 Web 界面启动成功!")
        print("访问: http://localhost:7777")
        uvicorn.run(app, host="localhost", port=7777)
        
    except ImportError:
        print("无法创建 Web 界面，缺少 fastapi 或 uvicorn")
        print("安装: pip install fastapi uvicorn")

def show_agent_info():
    """显示智能体信息"""
    
    agent = create_simple_agent()
    if not agent:
        return
    
    print("=== 智能体信息 ===")
    print(f"名称: {agent.name}")
    print(f"描述: {agent.description}")
    print(f"模型: {Config.MODELSCOPE_MODEL}")
    print(f"API: {Config.MODELSCOPE_BASE_URL}")
    
    # 检查 Agno 功能
    print("\\n=== Agno 功能检查 ===")
    
    functions_to_check = [
        'serve', 'run', 'print_response', 'app'
    ]
    
    for func in functions_to_check:
        if hasattr(agent, func):
            print(f"✓ {func}")
        else:
            print(f"✗ {func}")

def main():
    """主函数"""
    
    print("=== Agno 官方 Playground 启动器 ===\\n")
    
    print("选择操作:")
    print("1. 启动 Playground Web 服务")
    print("2. 测试智能体连接")
    print("3. 显示智能体信息")
    print("4. 检查 Agno 安装")
    print("5. 退出")
    
    try:
        choice = input("\\n请选择 (1-5): ").strip()
        
        if choice == "1":
            start_playground_server()
        elif choice == "2":
            test_agent()
        elif choice == "3":
            show_agent_info()
        elif choice == "4":
            check_agno_installation()
        elif choice == "5":
            print("退出")
        else:
            print("无效选择，启动 Playground")
            start_playground_server()
    
    except KeyboardInterrupt:
        print("\\n程序被用户中断")
    except Exception as e:
        print(f"程序发生错误: {e}")

def check_agno_installation():
    """检查 Agno 安装"""
    
    print("=== 检查 Agno 安装 ===\\n")
    
    try:
        import agno
        print(f"✓ Agno 版本: {agno.__version__}")
    except ImportError:
        print("✗ Agno 未安装")
        return
    except AttributeError:
        print("✓ Agno 已安装（版本信息不可用）")
    
    # 检查各个模块
    modules_to_check = [
        'agno.agent',
        'agno.models.openai', 
        'agno.playground'
    ]
    
    for module in modules_to_check:
        try:
            __import__(module)
            print(f"✓ {module}")
        except ImportError as e:
            print(f"✗ {module}: {e}")
    
    print("\\n建议:")
    print("- 确保使用最新版本: pip install -U agno")
    print("- 查看文档: https://docs.agno.com")

if __name__ == "__main__":
    main()