#!/usr/bin/env python3
"""
ModelScope Playground

专为 ModelScope API 设计的智能体 Playground
绕过 OpenAI 库限制，直接使用 requests 处理 API 调用
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import json
import time
from config import Config

class ModelScopeAgent:
    """自定义的 ModelScope 智能体"""
    
    def __init__(self, name="ModelScope Agent", description="", instructions=None):
        self.name = name
        self.description = description
        self.instructions = instructions or []
        self.conversation_history = []
        
        # API 配置
        self.api_url = f"{Config.MODELSCOPE_BASE_URL}/chat/completions"
        self.headers = {
            "Authorization": f"Bearer {Config.MODELSCOPE_API_KEY}",
            "Content-Type": "application/json"
        }
    
    def _build_messages(self, user_input):
        """构建消息列表"""
        messages = []
        
        # 添加系统指令
        if self.instructions:
            system_msg = "请遵循以下指令:\n" + "\n".join(f"- {inst}" for inst in self.instructions)
            messages.append({"role": "system", "content": system_msg})
        
        # 添加历史对话
        messages.extend(self.conversation_history)
        
        # 添加用户输入
        messages.append({"role": "user", "content": user_input})
        
        return messages
    
    def run(self, user_input, max_tokens=1000, temperature=0.7, stream=False):
        """运行智能体"""
        
        messages = self._build_messages(user_input)
        
        data = {
            "model": Config.MODELSCOPE_MODEL,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "enable_thinking": False  # ModelScope 必需参数
        }
        
        if stream:
            data["stream"] = True
            return self._stream_response(data, user_input)
        else:
            return self._normal_response(data, user_input)
    
    def _normal_response(self, data, user_input):
        """普通响应模式"""
        try:
            response = requests.post(self.api_url, headers=self.headers, json=data)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result['choices'][0]['message']['content']
                
                # 保存对话历史
                self.conversation_history.append({"role": "user", "content": user_input})
                self.conversation_history.append({"role": "assistant", "content": ai_response})
                
                # 限制历史长度
                if len(self.conversation_history) > 10:
                    self.conversation_history = self.conversation_history[-10:]
                
                return ai_response
            else:
                error_msg = f"API 错误: {response.status_code} - {response.text}"
                print(error_msg)
                return error_msg
                
        except Exception as e:
            error_msg = f"请求异常: {e}"
            print(error_msg)
            return error_msg
    
    def _stream_response(self, data, user_input):
        """流式响应模式"""
        try:
            response = requests.post(self.api_url, headers=self.headers, json=data, stream=True)
            
            if response.status_code == 200:
                full_response = ""
                
                for line in response.iter_lines():
                    if line:
                        line_text = line.decode('utf-8')
                        if line_text.startswith('data: '):
                            data_str = line_text[6:]
                            if data_str.strip() == '[DONE]':
                                break
                            
                            try:
                                chunk_data = json.loads(data_str)
                                if 'choices' in chunk_data and len(chunk_data['choices']) > 0:
                                    delta = chunk_data['choices'][0].get('delta', {})
                                    if 'content' in delta and delta['content']:
                                        content = delta['content']
                                        print(content, end='', flush=True)
                                        full_response += content
                            except json.JSONDecodeError:
                                continue
                
                # 保存对话历史
                self.conversation_history.append({"role": "user", "content": user_input})
                self.conversation_history.append({"role": "assistant", "content": full_response})
                
                # 限制历史长度
                if len(self.conversation_history) > 10:
                    self.conversation_history = self.conversation_history[-10:]
                
                return full_response
            else:
                error_msg = f"流式 API 错误: {response.status_code} - {response.text}"
                print(error_msg)
                return error_msg
                
        except Exception as e:
            error_msg = f"流式请求异常: {e}"
            print(error_msg)
            return error_msg
    
    def clear_history(self):
        """清除对话历史"""
        self.conversation_history = []

class ModelScopePlayground:
    """ModelScope Playground 管理器"""
    
    def __init__(self):
        self.agents = self._create_agents()
        self.current_agent_index = 0
    
    def _create_agents(self):
        """创建不同类型的智能体"""
        
        agents = [
            ModelScopeAgent(
                name="通用助手",
                description="我是一个友好的通用 AI 助手",
                instructions=[
                    "使用中文回答所有问题",
                    "保持友好和专业的语气",
                    "提供准确、详细的信息",
                    "如果不确定，请诚实说明"
                ]
            ),
            
            ModelScopeAgent(
                name="编程专家",
                description="我是一个专业的编程助手",
                instructions=[
                    "专注于编程和技术问题",
                    "提供清晰的代码示例",
                    "解释代码的工作原理",
                    "使用中文注释"
                ]
            ),
            
            ModelScopeAgent(
                name="创意写手",
                description="我是一个富有创意的写作助手",
                instructions=[
                    "发挥创意和想象力",
                    "使用生动有趣的语言",
                    "根据需求调整写作风格",
                    "保持内容的原创性"
                ]
            ),
            
            ModelScopeAgent(
                name="学习导师",
                description="我是一个耐心的学习指导老师",
                instructions=[
                    "以教学的方式解释概念",
                    "使用简单易懂的语言",
                    "提供学习建议和方法",
                    "鼓励和支持学习者"
                ]
            )
        ]
        
        return agents
    
    def get_current_agent(self):
        """获取当前智能体"""
        return self.agents[self.current_agent_index]
    
    def switch_agent(self, index):
        """切换智能体"""
        if 0 <= index < len(self.agents):
            self.current_agent_index = index
            return True
        return False
    
    def list_agents(self):
        """列出所有智能体"""
        for i, agent in enumerate(self.agents):
            marker = "→" if i == self.current_agent_index else " "
            print(f"  {marker} {i+1}. {agent.name} - {agent.description}")

def test_api_connection():
    """测试 API 连接"""
    print("=== 测试 ModelScope API 连接 ===\n")
    
    agent = ModelScopeAgent("测试智能体")
    response = agent.run("你好，请简单介绍一下自己", max_tokens=100)
    
    if "API 错误" not in response and "请求异常" not in response:
        print(f"✓ API 连接成功！")
        print(f"响应: {response}")
        return True
    else:
        print(f"✗ API 连接失败")
        return False

def interactive_playground():
    """交互式 Playground"""
    
    if not test_api_connection():
        print("请检查 API 配置")
        return
    
    playground = ModelScopePlayground()
    
    print(f"\n=== ModelScope Playground ===")
    print(f"模型: {Config.MODELSCOPE_MODEL}")
    print(f"API: {Config.MODELSCOPE_BASE_URL}")
    print("\n可用智能体:")
    playground.list_agents()
    
    print(f"\n当前智能体: {playground.get_current_agent().name}")
    print("\n命令说明:")
    print("- 'list': 查看所有智能体")
    print("- 'switch <数字>': 切换智能体")
    print("- 'clear': 清除当前对话历史")
    print("- 'quit': 退出 Playground")
    print("- 其他: 与智能体对话")
    print("\n开始对话:\n")
    
    while True:
        try:
            current_agent = playground.get_current_agent()
            user_input = input(f"[{current_agent.name}] 用户: ").strip()
            
            if user_input.lower() == 'quit':
                print("感谢使用 ModelScope Playground！")
                break
            
            elif user_input.lower() == 'list':
                print("\n可用智能体:")
                playground.list_agents()
                print()
                continue
            
            elif user_input.lower().startswith('switch '):
                try:
                    agent_num = int(user_input.split()[1]) - 1
                    if playground.switch_agent(agent_num):
                        new_agent = playground.get_current_agent()
                        print(f"\n已切换到: {new_agent.name}")
                        print(f"描述: {new_agent.description}\n")
                    else:
                        print("无效的智能体编号\n")
                except (IndexError, ValueError):
                    print("请输入有效的编号，例如: switch 2\n")
                continue
            
            elif user_input.lower() == 'clear':
                current_agent.clear_history()
                print("对话历史已清除\n")
                continue
            
            elif not user_input:
                continue
            
            # 与智能体对话
            print(f"{current_agent.name}: ", end="")
            current_agent.run(user_input, stream=True)
            print("\n")
            
        except KeyboardInterrupt:
            print("\n\nPlayground 被用户中断")
            break
        except Exception as e:
            print(f"发生错误: {e}")

def demo_mode():
    """演示模式"""
    
    print("=== ModelScope Playground 演示模式 ===\n")
    
    if not test_api_connection():
        return
    
    playground = ModelScopePlayground()
    
    demo_questions = [
        ("通用助手", "请介绍一下人工智能的发展历史"),
        ("编程专家", "请写一个 Python 函数来计算斐波那契数列"),
        ("创意写手", "写一首关于春天的小诗"),
        ("学习导师", "如何高效学习一门新的编程语言？")
    ]
    
    for agent_name, question in demo_questions:
        # 找到对应的智能体
        for i, agent in enumerate(playground.agents):
            if agent.name == agent_name:
                playground.switch_agent(i)
                break
        
        current_agent = playground.get_current_agent()
        print(f"【{current_agent.name}】")
        print(f"问题: {question}")
        print("回答: ", end="")
        
        current_agent.run(question, stream=True)
        print("\n" + "="*80 + "\n")
        
        # 询问是否继续
        if question != demo_questions[-1][1]:
            choice = input("按 Enter 继续下一个演示，或输入 'q' 退出: ").strip()
            if choice.lower() == 'q':
                break

def main():
    """主函数"""
    
    print("=== ModelScope Playground ===")
    print(f"模型: {Config.MODELSCOPE_MODEL}")
    print(f"API: {Config.MODELSCOPE_BASE_URL}\n")
    
    print("选择模式:")
    print("1. 交互式 Playground")
    print("2. 演示模式")
    print("3. 仅测试连接")
    
    try:
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == "1":
            interactive_playground()
        elif choice == "2":
            demo_mode()
        elif choice == "3":
            test_api_connection()
        else:
            print("无效选择，启动交互式 Playground")
            interactive_playground()
    
    except KeyboardInterrupt:
        print("\n程序被用户中断")

if __name__ == "__main__":
    main()