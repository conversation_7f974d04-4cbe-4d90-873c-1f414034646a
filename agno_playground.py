#!/usr/bin/env python3
"""
Agno 官方 Playground 启动脚本

使用 Agno 框架的官方 Playground Web 界面来测试和交互 ModelScope 智能体
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.playground import Playground, serve_playground_app
from config import Config
import logging

# 设置日志
logging.basicConfig(level=getattr(logging, Config.LOG_LEVEL))
logger = logging.getLogger(__name__)

def create_modelscope_agents():
    """创建 ModelScope 智能体用于 Playground"""
    
    if not Config.MODELSCOPE_API_KEY:
        print("错误: ModelScope API 密钥未配置")
        return []
    
    # 基础模型配置
    base_model = OpenAIChat(
        id=Config.MODELSCOPE_MODEL,
        api_key=Config.MODELSCOPE_API_KEY,
        base_url=Config.MODELSCOPE_BASE_URL
    )
    
    agents = []
    
    # 1. 通用助手
    general_agent = Agent(
        model=base_model,
        name="通用助手",
        description="我是一个通用的AI助手，可以帮助您解答各种问题。",
        instructions=[
            "使用中文回答",
            "保持友好和专业",
            "提供详细和准确的信息"
        ],
        markdown=True
    )
    agents.append(general_agent)
    
    # 2. 编程助手
    coding_agent = Agent(
        model=base_model,
        name="编程助手", 
        description="我是一个专业的编程助手，擅长各种编程语言和技术。",
        instructions=[
            "专注于编程相关问题",
            "提供清晰的代码示例",
            "解释代码的工作原理",
            "使用中文注释和说明"
        ],
        markdown=True
    )
    agents.append(coding_agent)
    
    # 3. 创意写作助手
    creative_agent = Agent(
        model=base_model,
        name="创意写作助手",
        description="我是一个创意写作助手，可以帮助您进行各种创意写作。",
        instructions=[
            "发挥创意和想象力",
            "使用生动的语言",
            "根据用户需求调整写作风格",
            "使用中文创作"
        ],
        markdown=True
    )
    agents.append(creative_agent)
    
    # 4. 学习导师
    tutor_agent = Agent(
        model=base_model,
        name="学习导师",
        description="我是一个学习导师，可以帮助您学习各种知识和技能。",
        instructions=[
            "以教学的方式解释概念",
            "使用简单易懂的语言",
            "提供学习建议和资源",
            "鼓励和支持学习者"
        ],
        markdown=True
    )
    agents.append(tutor_agent)
    
    return agents

def test_modelscope_connection():
    """测试 ModelScope 连接"""
    print("=== 测试 ModelScope API 连接 ===\n")
    
    try:
        agents = create_modelscope_agents()
        if not agents:
            return False
        
        # 测试第一个智能体
        test_agent = agents[0]
        print("正在测试连接...")
        
        response = test_agent.run("你好，请简单介绍一下自己")
        print(f"✓ 连接成功！")
        print(f"测试响应: {response.content[:100]}...")
        return True
        
    except Exception as e:
        print(f"✗ 连接失败: {e}")
        return False

def start_playground():
    """启动 Agno 官方 Playground"""
    
    print("=== 启动 Agno 官方 Playground ===\n")
    
    # 先测试连接
    if not test_modelscope_connection():
        print("ModelScope API 连接失败，请检查配置")
        return
    
    # 创建智能体
    agents = create_modelscope_agents()
    print(f"✓ 成功创建 {len(agents)} 个智能体")
    
    try:
        # 创建 Playground 应用
        print("\n正在启动 Agno Playground Web 界面...")
        print(f"模型: {Config.MODELSCOPE_MODEL}")
        print(f"API: {Config.MODELSCOPE_BASE_URL}")
        
        # 启动 Playground 服务
        serve_playground_app(
            agents=agents,
            host="localhost",
            port=7777,
            reload=True,
            debug=True
        )
        
    except Exception as e:
        logger.error(f"启动 Playground 失败: {e}")
        print(f"启动失败: {e}")
        
        # 尝试替代方案
        print("\\n尝试使用替代启动方式...")
        try:
            # 创建 Playground 实例
            playground = Playground(
                agents=agents,
                name="ModelScope Playground",
                description="基于 ModelScope 的智能体测试平台"
            )
            
            # 启动服务
            playground.serve(host="localhost", port=7777, debug=True)
            
        except Exception as e2:
            print(f"替代方案也失败: {e2}")
            print("请检查 Agno 版本和依赖")

def create_playground_app():
    """创建 Playground 应用（不自动启动服务）"""
    
    print("=== 创建 Agno Playground 应用 ===\\n")
    
    # 创建智能体
    agents = create_modelscope_agents()
    if not agents:
        print("无法创建智能体")
        return None
    
    print(f"✓ 成功创建 {len(agents)} 个智能体:")
    for i, agent in enumerate(agents, 1):
        print(f"  {i}. {agent.name}")
    
    # 创建 Playground 应用
    try:
        playground = Playground(
            agents=agents,
            name="ModelScope Playground",
            description="基于 ModelScope Qwen3-235B-A22B 的智能体平台",
            # 可以添加更多配置
            # theme="dark",
            # favicon="/path/to/favicon.ico"
        )
        
        print("\\n✓ Playground 应用创建成功")
        return playground
        
    except Exception as e:
        print(f"✗ Playground 应用创建失败: {e}")
        return None

def main():
    """主函数"""
    
    print("=== Agno 官方 Playground 启动器 ===\\n")
    print(f"ModelScope 配置:")
    print(f"  - 模型: {Config.MODELSCOPE_MODEL}")
    print(f"  - API: {Config.MODELSCOPE_BASE_URL}")
    print(f"  - 密钥: {Config.MODELSCOPE_API_KEY[:20]}...")
    print()
    
    print("选择运行模式:")
    print("1. 启动 Playground Web 服务 (推荐)")
    print("2. 创建 Playground 应用（不启动服务）")
    print("3. 仅测试 API 连接")
    print("4. 退出")
    
    try:
        choice = input("\\n请选择 (1-4): ").strip()
        
        if choice == "1":
            start_playground()
        elif choice == "2":
            playground = create_playground_app()
            if playground:
                print("\\nPlayground 应用已创建，您可以手动调用 serve() 方法启动服务")
                print("例如: playground.serve(host='localhost', port=7777)")
        elif choice == "3":
            test_modelscope_connection()
        elif choice == "4":
            print("退出")
        else:
            print("无效选择，启动 Playground 服务")
            start_playground()
    
    except KeyboardInterrupt:
        print("\\n程序被用户中断")
    except Exception as e:
        print(f"程序发生错误: {e}")

if __name__ == "__main__":
    main()