"""
Agno 智能体项目配置文件
"""
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """项目配置类"""
    
    # API Keys
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
    ANTHROPIC_API_KEY = os.getenv('ANTHROPIC_API_KEY')
    
    # ModelScope 配置
    MODELSCOPE_API_KEY = os.getenv('MODELSCOPE_API_KEY')
    MODELSCOPE_BASE_URL = os.getenv('MODELSCOPE_BASE_URL', 'https://api-inference.modelscope.cn/v1')
    MODELSCOPE_MODEL = os.getenv('MODELSCOPE_MODEL', 'Qwen/Qwen3-235B-A22B')
    
    # 项目信息
    PROJECT_NAME = os.getenv('PROJECT_NAME', 'agno_agent')
    VERSION = os.getenv('VERSION', '1.0.0')
    
    # 日志配置
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    
    # Agno 配置
    AGNO_TELEMETRY = os.getenv('AGNO_TELEMETRY', 'true').lower() == 'true'
    
    # 向量数据库配置
    VECTOR_DB_PATH = os.getenv('VECTOR_DB_PATH', './data/vectordb')
    
    # 默认模型配置
    DEFAULT_OPENAI_MODEL = "gpt-4o"
    DEFAULT_CLAUDE_MODEL = "claude-3-5-sonnet-latest"
    
    # 数据目录
    DATA_DIR = "./data"
    LOGS_DIR = "./logs"
    
    @classmethod
    def validate_api_keys(cls):
        """验证 API 密钥是否已设置"""
        missing_keys = []
        
        # 检查是否有 ModelScope 配置
        if cls.MODELSCOPE_API_KEY and cls.MODELSCOPE_BASE_URL:
            print("✓ ModelScope API 密钥已配置")
            return True
        
        # 检查传统 API 密钥
        if not cls.OPENAI_API_KEY:
            missing_keys.append('OPENAI_API_KEY')
        if not cls.ANTHROPIC_API_KEY:
            missing_keys.append('ANTHROPIC_API_KEY')
            
        if missing_keys and not cls.MODELSCOPE_API_KEY:
            print(f"警告: 以下 API 密钥未设置: {', '.join(missing_keys)}")
            print("请复制 .env.example 为 .env 并设置相应的 API 密钥")
            return False
        return True
    
    @classmethod
    def create_directories(cls):
        """创建必要的目录"""
        directories = [cls.DATA_DIR, cls.LOGS_DIR, cls.VECTOR_DB_PATH]
        for directory in directories:
            os.makedirs(directory, exist_ok=True)