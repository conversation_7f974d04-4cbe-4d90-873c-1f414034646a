#!/usr/bin/env python3
"""
Agno ModelScope 智能体项目

集成了 uv 虚拟环境管理、ModelScope API 和自定义 Playground 的完整智能体项目
"""

import sys
import os
import subprocess

def print_banner():
    """打印项目横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                        Agno ModelScope 智能体项目                           ║
║                                                                              ║
║  ✨ 使用 uv 管理虚拟环境和依赖                                              ║
║  🚀 集成 ModelScope Qwen3-235B-A22B 模型                                   ║
║  🎮 自定义 Playground 支持多智能体交互                                      ║
║  🔧 支持流式响应和对话历史管理                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)

def show_project_info():
    """显示项目信息"""
    print("📋 项目配置:")
    print(f"  - 虚拟环境: .venv/")
    print(f"  - 包管理: uv")
    print(f"  - 镜像源: 清华大学 PyPI 镜像")
    print(f"  - API 端点: https://api-inference.modelscope.cn/v1")
    print(f"  - 模型: Qwen/Qwen3-235B-A22B")
    print()

def show_available_tools():
    """显示可用工具"""
    tools = [
        ("agno_playground_fixed.py", "🎮 Agno Playground Web 界面 - 兼容 ModelScope API"),
        ("modelscope_playground.py", "🔧 自定义 Playground - 多智能体交互平台"),
        ("test_simple.py", "🧪 API 连接测试工具"),
        ("examples/modelscope_agent.py", "🤖 基础 ModelScope 智能体示例"),
        ("setup_uv.sh", "⚙️ 自动化环境搭建脚本"),
        ("test_project.py", "✅ 项目完整性测试")
    ]
    
    print("🛠️ 可用工具:")
    for tool, description in tools:
        status = "✓" if os.path.exists(tool) else "✗"
        print(f"  {status} {description}")
        print(f"    启动: python {tool}")
    print()

def check_environment():
    """检查环境状态"""
    print("🔍 环境检查:")
    
    # 检查虚拟环境
    if os.path.exists(".venv"):
        print("  ✓ 虚拟环境已创建")
    else:
        print("  ✗ 虚拟环境未创建")
        return False
    
    # 检查配置文件
    if os.path.exists(".env"):
        print("  ✓ 环境变量已配置")
    else:
        print("  ✗ 环境变量未配置")
        return False
    
    # 检查依赖
    try:
        sys.path.insert(0, os.path.abspath('.'))
        from config import Config
        if Config.MODELSCOPE_API_KEY:
            print("  ✓ ModelScope API 密钥已配置")
        else:
            print("  ✗ ModelScope API 密钥未配置")
            return False
    except ImportError:
        print("  ✗ 配置模块导入失败")
        return False
    
    return True

def quick_setup():
    """快速设置"""
    print("🚀 正在进行快速设置...")
    
    if not os.path.exists(".venv"):
        print("创建虚拟环境...")
        result = subprocess.run(["./setup_uv.sh"], capture_output=True, text=True)
        if result.returncode != 0:
            print("✗ 环境设置失败")
            print(result.stderr)
            return False
    
    print("✓ 环境设置完成")
    return True

def run_agno_playground():
    """运行 Agno Playground Web 界面"""
    print("🎮 启动 Agno Playground Web 界面...")
    
    if not check_environment():
        print("❌ 环境检查失败，请先运行环境设置")
        return
    
    try:
        subprocess.run([sys.executable, "agno_playground_fixed.py"])
    except KeyboardInterrupt:
        print("\n👋 Agno Playground 已退出")
    except Exception as e:
        print(f"❌ 启动 Agno Playground 失败: {e}")

def run_playground():
    """运行 Playground"""
    print("🎮 启动 ModelScope Playground...")
    
    if not check_environment():
        print("❌ 环境检查失败，请先运行环境设置")
        return
    
    try:
        subprocess.run([sys.executable, "modelscope_playground.py"])
    except KeyboardInterrupt:
        print("\n👋 Playground 已退出")
    except Exception as e:
        print(f"❌ 启动 Playground 失败: {e}")

def run_test():
    """运行测试"""
    print("🧪 运行 API 连接测试...")
    
    try:
        subprocess.run([sys.executable, "test_simple.py"])
    except KeyboardInterrupt:
        print("\n👋 测试已退出")
    except Exception as e:
        print(f"❌ 运行测试失败: {e}")

def show_menu():
    """显示主菜单"""
    menu = """
🎯 选择操作:

1. 🎮 启动 Agno Playground Web 界面 (推荐)
2. 🔧 启动自定义 ModelScope Playground
3. 🧪 测试 API 连接
4. ⚙️ 环境设置和检查
5. 📚 查看项目文档
6. 🛠️ 查看可用工具
7. 👋 退出

请选择 (1-7): """
    
    return input(menu).strip()

def show_documentation():
    """显示项目文档"""
    docs = """
📚 项目文档

🏗️ 项目结构:
├── modelscope_playground.py    # 主要的 Playground 应用
├── config.py                   # 项目配置管理
├── .env                        # 环境变量配置
├── setup_uv.sh                 # 自动化安装脚本
├── test_simple.py              # API 测试工具
├── examples/                   # 示例代码
│   └── modelscope_agent.py     # ModelScope 智能体示例
└── .venv/                      # uv 虚拟环境

🔧 使用方法:

1. 首次使用:
   chmod +x setup_uv.sh
   ./setup_uv.sh

2. 日常使用:
   source .venv/bin/activate
   python modelscope_playground.py

3. 测试连接:
   python test_simple.py

🎮 Playground 功能:

- 多智能体支持: 通用助手、编程专家、创意写手、学习导师
- 流式响应: 实时显示 AI 回答
- 对话历史: 自动管理上下文
- 智能体切换: 随时切换不同类型的助手
- 命令系统: 内置丰富的交互命令

💡 提示:

- 使用 'switch 2' 切换到编程专家
- 使用 'clear' 清除对话历史
- 使用 'list' 查看所有可用智能体
- 输入 'quit' 退出 Playground

🔗 相关链接:

- ModelScope: https://modelscope.cn/
- uv 文档: https://github.com/astral-sh/uv
- Agno 文档: https://docs.agno.com/
    """
    print(docs)

def main():
    """主函数"""
    print_banner()
    show_project_info()
    
    while True:
        try:
            choice = show_menu()
            
            if choice == '1':
                run_agno_playground()
            elif choice == '2':
                run_playground()
            elif choice == '3':
                run_test()
            elif choice == '4':
                if check_environment():
                    print("✅ 环境检查通过")
                else:
                    setup_choice = input("环境不完整，是否进行快速设置？(y/n): ").strip().lower()
                    if setup_choice == 'y':
                        quick_setup()
            elif choice == '5':
                show_documentation()
            elif choice == '6':
                show_available_tools()
            elif choice == '7':
                print("👋 感谢使用 Agno ModelScope 智能体项目！")
                break
            else:
                print("❌ 无效选择，请输入 1-7")
            
            input("\n按 Enter 键继续...")
            
        except KeyboardInterrupt:
            print("\n\n👋 程序被用户中断")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()