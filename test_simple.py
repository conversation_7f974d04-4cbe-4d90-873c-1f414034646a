#!/usr/bin/env python3
"""
直接使用 requests 测试 ModelScope API
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import json
from config import Config

def test_api_with_requests():
    """使用 requests 库直接测试 API"""
    
    print("=== 使用 requests 测试 ModelScope API ===\n")
    print(f"API 端点: {Config.MODELSCOPE_BASE_URL}")
    print(f"模型: {Config.MODELSCOPE_MODEL}")
    
    url = f"{Config.MODELSCOPE_BASE_URL}/chat/completions"
    headers = {
        "Authorization": f"Bearer {Config.MODELSCOPE_API_KEY}",
        "Content-Type": "application/json"
    }
    
    # 尝试不同的参数组合
    test_cases = [
        {
            "name": "基本测试",
            "data": {
                "model": Config.MODELSCOPE_MODEL,
                "messages": [
                    {"role": "user", "content": "你好"}
                ],
                "max_tokens": 100,
                "temperature": 0.7
            }
        },
        {
            "name": "包含 enable_thinking=false",
            "data": {
                "model": Config.MODELSCOPE_MODEL,
                "messages": [
                    {"role": "user", "content": "你好"}
                ],
                "max_tokens": 100,
                "temperature": 0.7,
                "enable_thinking": False
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"\\n--- {test_case['name']} ---")
        
        try:
            response = requests.post(url, headers=headers, json=test_case['data'])
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                print(f"✓ 成功! 响应: {content}")
                return True
            else:
                print(f"✗ 失败: {response.text}")
        
        except Exception as e:
            print(f"✗ 请求异常: {e}")
    
    return False

def create_agno_playground():
    """创建 Agno Playground"""
    
    print("\\n=== 创建 Agno Playground ===\\n")
    
    try:
        # 先测试基本的 OpenAI 调用
        from openai import OpenAI
        
        client = OpenAI(
            api_key=Config.MODELSCOPE_API_KEY,
            base_url=Config.MODELSCOPE_BASE_URL
        )
        
        print("测试 OpenAI 库基本调用...")
        
        # 尝试简化的调用
        response = client.chat.completions.create(
            model=Config.MODELSCOPE_MODEL,
            messages=[{"role": "user", "content": "Hi"}],
            max_tokens=50
        )
        
        print(f"✓ OpenAI 库调用成功: {response.choices[0].message.content}")
        
        # 现在尝试 Agno
        from agno.agent import Agent
        from agno.models.openai import OpenAIChat
        
        model = OpenAIChat(
            id=Config.MODELSCOPE_MODEL,
            api_key=Config.MODELSCOPE_API_KEY,
            base_url=Config.MODELSCOPE_BASE_URL
        )
        
        # 创建最基本的智能体
        agent = Agent(
            model=model,
            description="Simple assistant",
            instructions=["Be helpful"],
            markdown=False
        )
        
        print("测试 Agno Agent...")
        result = agent.run("Hello")
        print(f"✓ Agno Agent 成功: {result.content}")
        
        return agent
    
    except Exception as e:
        print(f"✗ 创建失败: {e}")
        return None

def interactive_mode(agent=None):
    """交互模式"""
    
    if not agent:
        print("没有可用的智能体")
        return
    
    print("\\n=== 交互模式 ===")
    print("输入 'quit' 退出\\n")
    
    while True:
        user_input = input("用户: ").strip()
        
        if user_input.lower() in ['quit', 'exit']:
            break
        
        if not user_input:
            continue
        
        try:
            print("AI: ", end="")
            agent.print_response(user_input, stream=True)
            print()
        except Exception as e:
            print(f"错误: {e}")

def main():
    """主函数"""
    
    print("ModelScope API 测试工具\\n")
    
    # 1. 测试基本 API 连接
    if test_api_with_requests():
        print("\\n✓ API 连接正常")
    else:
        print("\\n✗ API 连接失败，请检查配置")
        return
    
    # 2. 创建 Agno 智能体
    agent = create_agno_playground()
    
    if agent:
        print("\\n✓ Agno 智能体创建成功")
        
        # 3. 进入交互模式
        choice = input("\\n是否进入交互模式? (y/n): ").strip().lower()
        if choice == 'y':
            interactive_mode(agent)
    else:
        print("\\n✗ 无法创建 Agno 智能体")

if __name__ == "__main__":
    main()