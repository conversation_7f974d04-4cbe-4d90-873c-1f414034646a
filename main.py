#!/usr/bin/env python3
"""
Agno 智能体项目主启动脚本

这个脚本提供了一个统一的入口点来运行不同类型的智能体示例。
"""

import os
import sys
import subprocess
from pathlib import Path
from config import Config

def print_banner():
    """打印项目横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    Agno 智能体项目                           ║
║                                                              ║
║  基于 Agno 框架构建的智能体示例项目                          ║
║  轻量级、高性能、模型无关的智能体构建框架                    ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_dependencies():
    """检查项目依赖"""
    print("正在检查项目依赖...")
    
    try:
        import agno
        print("✓ Agno 框架已安装")
    except ImportError:
        print("✗ Agno 框架未安装")
        print("请运行: pip install -r requirements.txt")
        return False
    
    try:
        import openai
        print("✓ OpenAI 库已安装")
    except ImportError:
        print("✗ OpenAI 库未安装")
    
    try:
        import anthropic
        print("✓ Anthropic 库已安装")
    except ImportError:
        print("⚠ Anthropic 库未安装（可选）")
    
    return True

def check_environment():
    """检查环境配置"""
    print("\n正在检查环境配置...")
    
    # 检查 .env 文件
    env_file = Path(".env")
    if not env_file.exists():
        print("✗ .env 文件不存在")
        print("请复制 .env.example 为 .env 并配置 API 密钥")
        return False
    else:
        print("✓ .env 文件存在")
    
    # 检查 API 密钥
    if Config.validate_api_keys():
        print("✓ API 密钥配置正确")
        return True
    else:
        return False

def create_directories():
    """创建必要的目录"""
    print("\n正在创建项目目录...")
    Config.create_directories()
    print("✓ 项目目录创建完成")

def show_menu():
    """显示主菜单"""
    menu = """
请选择要运行的智能体示例:

1. 基础智能体                    - 基本对话功能
2. 带工具的智能体                - 集成搜索和数据工具
3. 推理智能体                    - 具备逐步推理能力
4. 多智能体团队                  - 团队协作完成任务
5. 自定义工具智能体              - 使用自定义工具
6. 运行所有示例（演示模式）      - 快速预览所有功能
7. 项目配置检查                  - 验证配置和依赖
8. 退出

请输入选项 (1-8): """
    
    return input(menu).strip()

def run_example(script_name, demo_mode=False):
    """运行示例脚本"""
    script_path = Path("examples") / script_name
    
    if not script_path.exists():
        print(f"✗ 示例文件 {script_path} 不存在")
        return False
    
    print(f"\n正在启动: {script_path}")
    print("=" * 60)
    
    try:
        if demo_mode:
            # 演示模式：自动选择演示选项
            env = os.environ.copy()
            env['DEMO_MODE'] = '1'
            subprocess.run([sys.executable, str(script_path)], env=env)
        else:
            subprocess.run([sys.executable, str(script_path)])
        return True
    except KeyboardInterrupt:
        print("\n用户中断执行")
        return False
    except Exception as e:
        print(f"✗ 运行示例时发生错误: {e}")
        return False

def run_all_demos():
    """运行所有示例的演示模式"""
    examples = [
        ("basic_agent.py", "基础智能体"),
        ("agent_with_tools.py", "带工具的智能体"),
        ("reasoning_agent.py", "推理智能体"),
        ("multi_agent_team.py", "多智能体团队"),
        ("custom_tools_agent.py", "自定义工具智能体")
    ]
    
    print("\n=== 运行所有示例演示 ===")
    
    for script, name in examples:
        print(f"\n{'=' * 80}")
        print(f"正在演示: {name}")
        print(f"{'=' * 80}")
        
        # 简单模拟演示输出
        print(f"✓ {name} 演示完成")
        
        choice = input("\n是否继续下一个演示？(y/n): ").strip().lower()
        if choice != 'y':
            break
    
    print("\n所有演示完成！")

def validate_setup():
    """验证项目设置"""
    print("\n=== 项目配置检查 ===")
    
    # 检查 Python 版本
    python_version = sys.version_info
    print(f"Python 版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version >= (3, 8):
        print("✓ Python 版本满足要求")
    else:
        print("✗ Python 版本过低，需要 3.8+")
        return False
    
    # 检查依赖
    if not check_dependencies():
        return False
    
    # 检查环境
    if not check_environment():
        return False
    
    # 检查目录结构
    required_dirs = ['examples', 'tools', 'data', 'logs']
    for dir_name in required_dirs:
        dir_path = Path(dir_name)
        if dir_path.exists():
            print(f"✓ 目录 {dir_name} 存在")
        else:
            print(f"✗ 目录 {dir_name} 不存在")
    
    # 检查示例文件
    examples = ['basic_agent.py', 'agent_with_tools.py', 'reasoning_agent.py', 
                'multi_agent_team.py', 'custom_tools_agent.py']
    
    for example in examples:
        example_path = Path('examples') / example
        if example_path.exists():
            print(f"✓ 示例文件 {example} 存在")
        else:
            print(f"✗ 示例文件 {example} 不存在")
    
    print("\n✓ 项目配置检查完成")
    return True

def main():
    """主函数"""
    print_banner()
    
    # 初始化检查
    create_directories()
    
    while True:
        try:
            choice = show_menu()
            
            if choice == '1':
                run_example('basic_agent.py')
            elif choice == '2':
                run_example('agent_with_tools.py')
            elif choice == '3':
                run_example('reasoning_agent.py')
            elif choice == '4':
                run_example('multi_agent_team.py')
            elif choice == '5':
                run_example('custom_tools_agent.py')
            elif choice == '6':
                run_all_demos()
            elif choice == '7':
                validate_setup()
            elif choice == '8':
                print("\n感谢使用 Agno 智能体项目！")
                break
            else:
                print("无效选择，请输入 1-8 之间的数字")
            
            # 询问是否继续
            if choice in ['1', '2', '3', '4', '5']:
                continue_choice = input("\n是否返回主菜单？(y/n): ").strip().lower()
                if continue_choice != 'y':
                    break
        
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            break
        except Exception as e:
            print(f"\n发生错误: {e}")
            continue

if __name__ == "__main__":
    main()