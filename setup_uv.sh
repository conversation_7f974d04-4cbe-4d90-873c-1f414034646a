#!/bin/bash

# Agno 智能体项目初始化脚本 (使用 uv)
# 此脚本使用 uv 管理虚拟环境和依赖，并配置国内镜像源

set -e  # 遇到错误时退出

echo "======================================"
echo "  Agno 智能体项目初始化脚本 (uv)"
echo "======================================"

# 检查 uv 是否已安装
if ! command -v uv &> /dev/null; then
    echo "✗ uv 未安装"
    echo "正在安装 uv..."
    
    # 安装 uv
    if command -v curl &> /dev/null; then
        curl -LsSf https://astral.sh/uv/install.sh | sh
        export PATH="$HOME/.cargo/bin:$PATH"
    else
        echo "请先安装 curl 或手动安装 uv"
        echo "访问: https://github.com/astral-sh/uv"
        exit 1
    fi
    
    # 验证安装
    if ! command -v uv &> /dev/null; then
        echo "✗ uv 安装失败"
        exit 1
    fi
fi

echo "✓ uv 已安装: $(uv --version)"

# 检查 Python 版本
echo "检查 Python 版本..."
python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" = "$required_version" ]; then
    echo "✓ Python 版本 $python_version 满足要求"
else
    echo "✗ Python 版本过低。需要 Python 3.8+，当前版本: $python_version"
    exit 1
fi

# 创建虚拟环境
echo "创建虚拟环境..."
if [ -d ".venv" ]; then
    echo "⚠ 虚拟环境已存在，正在重新创建..."
    rm -rf .venv
fi

uv venv --python python3
echo "✓ 虚拟环境创建完成"

# 激活虚拟环境
echo "激活虚拟环境..."
source .venv/bin/activate
echo "✓ 虚拟环境已激活"

# 配置 uv 使用国内镜像源
echo "配置国内镜像源..."
export UV_INDEX_URL="https://pypi.tuna.tsinghua.edu.cn/simple"
export UV_EXTRA_INDEX_URL="https://mirrors.aliyun.com/pypi/simple"
echo "✓ 镜像源配置完成"

# 使用 uv 安装依赖
echo "安装项目依赖..."
echo "使用镜像源: $UV_INDEX_URL"

# 先安装核心依赖
uv pip install agno openai python-dotenv

# 安装所有依赖
if [ -f "pyproject.toml" ]; then
    echo "从 pyproject.toml 安装依赖..."
    uv pip install -e .
else
    echo "从 requirements.txt 安装依赖..."
    uv pip install -r requirements.txt
fi

echo "✓ 依赖安装完成"

# 创建环境变量文件
if [ ! -f .env ]; then
    echo "创建环境变量文件..."
    cp .env.example .env
    echo "✓ .env 文件已创建"
    echo "⚠ 请编辑 .env 文件并设置您的 API 密钥"
else
    echo "✓ .env 文件已存在"
fi

# 创建必要目录
echo "创建项目目录..."
mkdir -p data logs .uv-cache

echo "✓ 项目目录创建完成"

# 验证安装
echo "验证安装..."
if .venv/bin/python -c "import agno; print('✓ Agno 安装成功')" 2>/dev/null; then
    echo "✓ 核心依赖验证成功"
else
    echo "✗ 核心依赖验证失败"
    exit 1
fi

# 显示虚拟环境信息
echo ""
echo "======================================"
echo "        初始化完成！"
echo "======================================"
echo ""
echo "虚拟环境信息："
echo "- 位置: $(pwd)/.venv"
echo "- Python: $(.venv/bin/python --version)"
echo "- uv 版本: $(uv --version)"
echo ""
echo "使用说明："
echo "1. 激活虚拟环境: source .venv/bin/activate"
echo "2. 编辑 .env 文件设置 API 密钥"
echo "3. 运行测试: python examples/modelscope_agent.py"
echo "4. 使用 Playground: python playground_test.py"
echo ""
echo "依赖管理："
echo "- 添加依赖: uv pip install package_name"
echo "- 列出依赖: uv pip list"
echo "- 导出依赖: uv pip freeze > requirements.txt"
echo ""

# 询问是否立即运行测试
read -p "是否立即测试 ModelScope 智能体？(y/n): " test_agent

if [[ $test_agent == "y" || $test_agent == "Y" ]]; then
    echo "启动 ModelScope 智能体测试..."
    .venv/bin/python examples/modelscope_agent.py
fi